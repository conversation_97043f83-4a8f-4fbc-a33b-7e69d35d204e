import { Module } from '@nestjs/common';
import { CompanyScrapperService } from './company_scrapper.service';
import { CompanyScrapperController } from './company_scrapper.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CompanyScrapper } from './entities/company_scrapper.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CompanyScrapper])],
  controllers: [CompanyScrapperController],
  providers: [CompanyScrapperService],
})
export class CompanyScrapperModule {}
