import React, { useCallback, useState, useMemo, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  Background,
  Controls,
  MiniMap,
  addEdge,
  useEdgesState,
  useNodesState,
  Panel,
  MarkerType,
  ReactFlow,
  Handle,
  Position
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Button, Input, Divider, Tooltip, Space, message } from 'antd';
import {
  DeleteOutlined,
  EditOutlined,
  UndoOutlined,
  RedoOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import {
  MailOutlined,
  MessageOutlined,
  PhoneOutlined,
  WhatsAppOutlined,
  LinkedinOutlined,
} from '@ant-design/icons';
import EditSequenceTemplateDialog from './EditSequenceTemplateDialog';
import FollowUpStepManager from './FollowUpStepManager';
import { Icon } from '@iconify/react';

const PROXIMITY_RADIUS = 150;

const getInitialNodes = (delete<PERSON><PERSON><PERSON>, edit<PERSON><PERSON><PERSON>, followUp<PERSON>and<PERSON>) => [
  {
    id: 'start',
    type: 'input',
    data: {
      label: 'Start Sequence',
      nodeId: 'start',
      onDelete: deleteHandler,
      onEdit: editHandler,
      onAddFollowUp: followUpHandler,
    },
    position: { x: 100, y: 100 },
    style: {
      border: '2px solid #1A84DE',
      borderRadius: 8,
      padding: 8,
      background: '#fff',
    },
  },
];


const getDetailedTooltip = (statType, channel) => {
  const channelType = channel?.toLowerCase();

  if (statType === 'failed') {
    if (channelType.includes('email')) {
      return (
        <div>
          <div>Backend Issue: 2</div>
          <div>Email Invalid: 1</div>
          <div>Email Bounced: 1</div>
          <div>Left Company: 1</div>
        </div>
      );
    } else if (channelType.includes('sms') || channelType.includes('whatsapp')) {
      return (
        <div>
          <div>Backend Issue: 1</div>
          <div>Number Invalid: 2</div>
        </div>
      );
    } else if (channelType.includes('li')) {
      return (
        <div>
          <div>Scrapper Failure: 1</div>
          <div>Backend Failure: 1</div>
          <div>Profile Restricted: 2</div>
        </div>
      );
    }
  } else if (statType === 'replies' && channelType.includes('email')) {
    return (
      <div>

        <div style={{ marginTop: '5px' }}>
          <div>Auto Replies: 3</div>
          <div style={{ marginLeft: '10px', marginTop: '2px' }}>
            <div>Left company 4</div>
            <div>At leave: 3</div>
          </div>
        </div>
        <div style={{ marginTop: '5px' }}>
          <strong>Potential Replies: 7</strong>
          <div style={{ marginLeft: '10px', marginTop: '2px' }}>
            <div>Interested: 4</div>
            <div>Not Interested: 3</div>
          </div>
        </div>
      </div>
    );
  } else if (statType === 'replies') {
    return (
      <div>


        <div>Auto Replies: 3</div>


        <div style={{ marginTop: '5px' }}>
          <strong>Potential Replies: 7</strong>
          <div style={{ marginLeft: '10px', marginTop: '2px' }}>
            <div>Interested: 4</div>
            <div>Not Interested: 3</div>
          </div>
        </div>
      </div>
    )
  }
  else if (statType === 'accepted' && channelType.includes('li')) {
    return (
      <div>
        <div>
          <strong>Replied: 5</strong>
          <div style={{ marginLeft: '10px', marginTop: '2px' }}>
            <div>Interested: 3</div>
            <div>Not Interested: 2</div>
          </div>
        </div>
        <div style={{ marginTop: '5px' }}>Not Replied: 8</div>
      </div>
    );
  }

  return null;
};

const getResponseStats = (channel) => {
  const channelType = channel?.toLowerCase();
  let stats = [];

  if (channelType.includes('li')) {
    // LinkedIn stats
    stats = [
      { icon: <Icon icon="mdi:success" width="20" height="20" />, label: 'Sent', value: 25, color: '#52c41a' },
      { icon: <Icon icon="oui:cross" width="20" height="20" />, label: 'Failed', value: 4, color: '#ff4d4f', hasTooltip: true },
      { icon: <Icon icon="healthicons:i-documents-accepted-outline" width="20" height="20" />, label: 'Accepted', value: 13, color: '#1890ff', hasTooltip: true },
      { icon: <Icon icon="marketeq:rejected-file-2" width="22" height="22" />, label: 'Not Accepted', value: 8, color: '#faad14' },
    ];
  } else {
    // Email, SMS, WhatsApp, Call stats
    stats = [
      { icon: <Icon icon="mdi:success" width="20" height="20" />, label: 'Success', value: 45, color: '#52c41a' },
      { icon: <Icon icon="oui:cross" width="20" height="20" />, label: 'Failed', value: 4, color: '#ff4d4f', hasTooltip: true },
      { icon: <Icon icon="bx:message-x" width="22" height="22" />, label: 'No Response', value: 12, color: '#faad14' },
      { icon: <Icon icon="teenyicons:message-tick-outline" width="20" height="20" />, label: 'Replies', value: 10, color: '#1890ff', hasTooltip: true },
    ];
  }

  return (
    <div
      style={{
        padding: '10px 16px',
        display: 'flex',
        justifyContent: 'space-between',
        fontSize: 12,
        pointerEvents: 'auto',
        zIndex: 10,
        position: 'relative',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #e9ecef',
      }}
    >
      {stats.map((stat, idx) => (
        <Tooltip
          key={idx}
          title={stat.hasTooltip ? getDetailedTooltip(stat.label.toLowerCase(), channel) : stat.label}
          getPopupContainer={() => document.body}
          placement='top'
          overlayStyle={{ maxWidth: '200px' }}
        >
          <div
            style={{
              color: stat.color,
              cursor: 'default',
              textAlign: 'center',
              fontSize: '11px',
            }}
          >
            <div>{stat.icon}</div>
            <div style={{ fontWeight: 'bold', marginTop: '2px' }}>{stat.value}</div>
            <div style={{ fontSize: '9px', opacity: 0.8 }}>{stat.label}</div>
          </div>
        </Tooltip>
      ))}
    </div>
  );
};


const getStepIcon = (channel) => {
  switch (channel.toLowerCase()) {
    case 'email':
      return <MailOutlined style={{ fontSize: 18 }} />;
    case 'sms':
      return <MessageOutlined style={{ fontSize: 18 }} />;
    case 'call':
      return <PhoneOutlined style={{ fontSize: 18 }} />;
    case 'whatsapp':
      return <WhatsAppOutlined style={{ fontSize: 18 }} />;
    case 'li connection':
    case 'li inmail':
      return <LinkedinOutlined style={{ fontSize: 18 }} />;
    case 'email_response':
      return <MailOutlined style={{ fontSize: 18 }} />;
    case 'sms_response':
      return <MessageOutlined style={{ fontSize: 18 }} />;
    case 'call_response':
      return <PhoneOutlined style={{ fontSize: 18 }} />;
    case 'whatsapp_response':
      return <WhatsAppOutlined style={{ fontSize: 18 }} />;
    case 'li connection_response':
    case 'li inmail_response':
      return <LinkedinOutlined style={{ fontSize: 18 }} />;
    default:
      return <MailOutlined style={{ fontSize: 18 }} />;
  }
};


const CustomNode = React.memo(({ data }) => {
  // Memoized to prevent unnecessary re-renders
  const stepIcon = useMemo(() => getStepIcon(data.channel), [data.channel]);
  const responseStats = useMemo(() => {
    return data.label?.toLowerCase().includes('response') ? getResponseStats(data.channel) : null;
  }, [data.label, data.channel]);

  // Determine if this is a response step
  const isResponseStep = data.label?.toLowerCase().includes('response');
  const backgroundColor = isResponseStep ? '#daf2d9' : '#cfe8fc';
  const iconColor = isResponseStep ? '#3aa537' : '#2196F3';

  return (
    <div
      style={{
        border: '1px solid #ddd',
        borderRadius: 6,
        background: '#fff',
        width: 400,
        fontFamily: 'sans-serif',
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
      }}
    >
      <div style={{ padding: '6px 12px', fontSize: 12, color: '#555' }}>
        Queue : {data.queue || 100}
        {data.delay > 0 && (
          <span style={{ marginLeft: '10px', color: '#666' }}>
            | Delay: {data.delay} min{data.delay !== 1 ? 's' : ''}
          </span>
        )}
        {data.hasFollowUp && (
          <span style={{ marginLeft: '10px', color: '#1890ff' }}>
            | Follow-up: {data.followUpSteps?.length || 0} step(s)
          </span>
        )}
        <div style={{ float: 'right', display: 'flex', gap: '10px' }}>
          {!isResponseStep && (
            <span
              style={{ cursor: 'pointer', color: '#1890ff' }}
              onClick={(e) => {
                e.stopPropagation();
                if (data.onAddFollowUp) {
                  data.onAddFollowUp(data);
                }
              }}
            >
              <ClockCircleOutlined />
              &nbsp;Follow-up
            </span>
          )}
          <span
            style={{ cursor: 'pointer', color: '#999' }}
            onClick={(e) => {
              e.stopPropagation();
              if (data.onDelete) {
                data.onDelete(data.nodeId);
              }
            }}
          >
            <DeleteOutlined />
            &nbsp;Delete
          </span>
        </div>
      </div>
      <div
        style={{
          backgroundColor: backgroundColor,
          height: '45px',
          color: isResponseStep ? '#3aa537' : '#0d8bf2',
          padding: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <div
            style={{
              background: '#fff',
              height: "25px",
              width: '32px',
              color: iconColor,
              padding: 6,
              borderRadius: 6,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {stepIcon}
          </div>
          <strong>{data.label}</strong>
        </div>
        {!isResponseStep && (
          <div
            style={{ cursor: 'pointer' }}
            onClick={(e) => {
              e.stopPropagation();
              if (data.onEdit) {
                data.onEdit(data);
              }
            }}
          >
            <EditOutlined />
            &nbsp;Edit
          </div>
        )}
      </div>
      {responseStats}



      {/* Handles for React Flow connections */}
      <Handle type="target" position={Position.Top} />
      <Handle type="source" position={Position.Bottom} />
    </div>
  );
});

const SequenceBuilder = () => {
  const { roleId: paramRoleId } = useParams(); // Get roleId from URL params
  const user = useSelector((state) => state.auth?.user || state.user); // Get user from Redux

  // Handle follow-up step management
  const handleAddFollowUp = useCallback((stepData) => {
    setSelectedStepForFollowUp(stepData);
    setFollowUpDialogOpen(true);
  }, []);

  // Temporary delete function for initial nodes (will be updated later)
  const tempDeleteNode = useCallback((nodeId) => {
    if (nodeId === 'start') return; // Prevent deleting start node
    console.log('Delete node:', nodeId);
  }, []);

  const handleEditStep = useCallback((stepData) => {
    setEditingStep(stepData);
    setEditDialogOpen(true);
  }, []);

  const [nodes, setNodes, handleNodesChange] = useNodesState(getInitialNodes(tempDeleteNode, handleEditStep, handleAddFollowUp));
  // State for edit dialog
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingStep, setEditingStep] = useState(null);
  const [sequenceName, setSequenceName] = useState('');

  // State for follow-up dialog
  const [followUpDialogOpen, setFollowUpDialogOpen] = useState(false);
  const [selectedStepForFollowUp, setSelectedStepForFollowUp] = useState(null);

  // Handle edit step


  // Handle dialog close with data
  const handleDialogClose = useCallback((data) => {
    if (data && editingStep) {
      // Update the node with new data including follow-up configuration
      setNodes((nds) =>
        nds.map((node) =>
          node.id === editingStep.nodeId
            ? {
              ...node,
              data: {
                ...node.data,
                delay: data.delay || 0,
                subject: data.subject || '',
                templateContent: data.templateContent || '',
                hasFollowUp: data.hasFollowUp || false,
                followUpSteps: data.followUpSteps || []
              }
            }
            : node
        )
      );

      // Show success message if follow-ups were configured
      if (data.hasFollowUp && data.followUpSteps?.length > 0) {
        message.success(`Follow-up configuration saved for "${editingStep.label}" with ${data.followUpSteps.length} follow-up step(s)`);
      }
    }
    setEditDialogOpen(false);
    setEditingStep(null);
  }, [editingStep, setNodes]);

  const handleFollowUpDialogClose = useCallback(() => {
    setFollowUpDialogOpen(false);
    setSelectedStepForFollowUp(null);
  }, []);

  const handleFollowUpCreated = useCallback(() => {
    message.success('Follow-up steps created successfully');
    // Optionally refresh the sequence data or update UI
  }, []);

  const nodeTypes = useMemo(() => ({
    custom: CustomNode,
  }), []);





  const [edges, setEdges, handleEdgesChange] = useEdgesState([]);

  // Undo/Redo state management
  const [edgeHistory, setEdgeHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const maxHistorySize = 50;

  // Helper function to save edge state to history
  const saveEdgeState = useCallback((newEdges) => {
    setEdgeHistory((history) => {
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push([...newEdges]);

      // Limit history size
      if (newHistory.length > maxHistorySize) {
        newHistory.shift();
        setHistoryIndex((prev) => prev);
      } else {
        setHistoryIndex((prev) => prev + 1);
      }

      return newHistory;
    });
  }, [historyIndex, maxHistorySize]);

  // Undo function
  const undoEdgeChange = useCallback(() => {
    if (historyIndex > 0) {
      const previousEdges = edgeHistory[historyIndex - 1];
      setEdges(previousEdges);
      setHistoryIndex((prev) => prev - 1);
    }
  }, [historyIndex, edgeHistory, setEdges]);

  // Redo function
  const redoEdgeChange = useCallback(() => {
    if (historyIndex < edgeHistory.length - 1) {
      const nextEdges = edgeHistory[historyIndex + 1];
      setEdges(nextEdges);
      setHistoryIndex((prev) => prev + 1);
    }
  }, [historyIndex, edgeHistory, setEdges]);

  // Check if undo/redo is available
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < edgeHistory.length - 1;

  // Initialize edge history with empty state
  useEffect(() => {
    if (edgeHistory.length === 0) {
      setEdgeHistory([[]]);
      setHistoryIndex(0);
    }
  }, [edgeHistory.length]);

  // Keyboard shortcuts for undo/redo
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Check for Ctrl+Z (undo)
      if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
        event.preventDefault();
        if (canUndo) {
          undoEdgeChange();
        }
      }
      // Check for Ctrl+Y or Ctrl+Shift+Z (redo)
      else if (event.ctrlKey && (event.key === 'y' || (event.key === 'z' && event.shiftKey))) {
        event.preventDefault();
        if (canRedo) {
          redoEdgeChange();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [canUndo, canRedo, undoEdgeChange, redoEdgeChange]);

  // Custom edge change handler that saves to history
  const onEdgesChange = useCallback((changes) => {
    handleEdgesChange(changes);

    // Save to history for delete operations
    const hasDeleteChanges = changes.some(change => change.type === 'remove');
    if (hasDeleteChanges) {
      setEdges((currentEdges) => {
        // Apply the changes first
        let newEdges = [...currentEdges];
        changes.forEach(change => {
          if (change.type === 'remove') {
            newEdges = newEdges.filter(edge => edge.id !== change.id);
          }
        });

        // Save to history
        saveEdgeState(newEdges);
        return newEdges;
      });
    }
  }, [handleEdgesChange, setEdges, saveEdgeState]);

  // Helper function to find connected step-response pairs (defined early)
  const findConnectedPair = useCallback((nodeId) => {
    // Check if this is a step node (find its response)
    if (nodeId.includes('-') && !nodeId.includes('response')) {
      const responseId = nodeId.replace('-', '-response-');
      return responseId;
    }
    // Check if this is a response node (find its step)
    if (nodeId.includes('response')) {
      const stepId = nodeId.replace('-response-', '-');
      return stepId;
    }
    return null;
  }, []);

  // Delete node function (deletes both step and response)
  const deleteNode = useCallback((nodeId) => {
    if (nodeId === 'start') return; // Prevent deleting start node

    // Find connected pair
    const connectedNodeId = findConnectedPair(nodeId);
    const nodesToDelete = connectedNodeId ? [nodeId, connectedNodeId] : [nodeId];

    setNodes((nds) => nds.filter(node => !nodesToDelete.includes(node.id)));
    setEdges((eds) => {
      const newEdges = eds.filter(edge =>
        !nodesToDelete.includes(edge.source) && !nodesToDelete.includes(edge.target)
      );
      saveEdgeState(newEdges);
      return newEdges;
    });
  }, [setNodes, setEdges, saveEdgeState, findConnectedPair]);

  const onNodesChange = useCallback(
    (changes) => {
      // Handle position changes for connected pairs
      const positionChanges = changes.filter(change => change.type === 'position');

      if (positionChanges.length > 0) {
        const enhancedChanges = [...changes];

        positionChanges.forEach((change) => {
          const connectedNodeId = findConnectedPair(change.id);
          if (connectedNodeId && change.position) {
            // Find if the connected node already has a position change
            const existingChange = enhancedChanges.find(c => c.id === connectedNodeId && c.type === 'position');

            if (!existingChange) {
              // Calculate connected node position
              let connectedPosition;
              if (change.id.includes('response')) {
                // This is a response node, connected node (step) should be above
                connectedPosition = {
                  x: change.position.x,
                  y: change.position.y - 100
                };
              } else {
                // This is a step node, connected node (response) should be below
                connectedPosition = {
                  x: change.position.x,
                  y: change.position.y + 100
                };
              }

              // Add position change for connected node
              enhancedChanges.push({
                id: connectedNodeId,
                type: 'position',
                position: connectedPosition,
                dragging: change.dragging
              });
            }
          }
        });

        // Apply enhanced changes
        handleNodesChange(enhancedChanges);
      } else {
        // Apply standard changes for non-position changes
        handleNodesChange(changes);
      }

      // Process position changes for ordering (only when dragging stops)
      const finalPositionChanges = changes.filter(
        (change) => change.type === 'position' && change.dragging === false
      );

      if (finalPositionChanges.length === 0) return;

      // Use functional update to get current nodes state
      setNodes((currentNodes) => {
        // Sort nodes by y position for ordering
        const sorted = [...currentNodes].sort((a, b) => a.position.y - b.position.y);

        // Group y positions within tolerance (parallel)
        const groups = [];
        const tolerance = 50; // Increased tolerance for step-response pairs

        sorted.forEach((node) => {
          const group = groups.find((g) => Math.abs(g.y - node.position.y) <= tolerance);
          if (group) {
            group.nodes.push(node);
          } else {
            groups.push({ y: node.position.y, nodes: [node] });
          }
        });

        // Assign order: start = 0, then 1, 2, 3...
        const orderedNodes = sorted.map((node) => {
          if (node.id === 'start') {
            return {
              ...node,
              data: { ...node.data, order: 0 },
            };
          }

          const groupIndex = groups.findIndex((g) =>
            g.nodes.find((n) => n.id === node.id)
          );

          return {
            ...node,
            data: { ...node.data, order: groupIndex + 1 },
          };
        });

        return orderedNodes;
      });

      // Handle auto-connect logic (disabled for step-response pairs)
      finalPositionChanges.forEach((change) => {
        // Skip auto-connect for step-response pairs
        if (findConnectedPair(change.id)) return;

        setNodes((currentNodes) => {
          setEdges((currentEdges) => {
            const moved = currentNodes.find((n) => n.id === change.id);
            if (!moved) return currentEdges;

            const nearby = currentNodes.find((node) => {
              if (node.id === moved.id) return false;
              if (findConnectedPair(node.id)) return false; // Skip step-response pairs
              const dx = node.position.x - moved.position.x;
              const dy = node.position.y - moved.position.y;
              return Math.sqrt(dx * dx + dy * dy) <= PROXIMITY_RADIUS;
            });

            if (nearby) {
              const alreadyConnected = currentEdges.some(
                (e) => e.source === nearby.id && e.target === moved.id
              );

              if (!alreadyConnected) {
                // Check if this would be a step-response connection
                const isStepResponseConnection =
                  findConnectedPair(nearby.id) === moved.id ||
                  findConnectedPair(moved.id) === nearby.id;

                return addEdge(
                  {
                    id: `e-${nearby.id}-${moved.id}`,
                    source: nearby.id,
                    target: moved.id,
                    markerEnd: { type: MarkerType.ArrowClosed },
                    animated: !isStepResponseConnection,
                    style: isStepResponseConnection ? {
                      stroke: '#000000',
                      strokeWidth: 2
                    } : undefined,
                    type: isStepResponseConnection ? 'step-response' : 'default'
                  },
                  currentEdges
                );
              }
            }

            return currentEdges;
          });
          return currentNodes;
        });
      });
    },
    [handleNodesChange, setNodes, setEdges, findConnectedPair]
  );




  const onConnect = useCallback(
    (params) => {
      setEdges((eds) => {
        // Check if this is a step-response connection
        const isStepResponseConnection =
          (params.source && params.target && findConnectedPair(params.source) === params.target) ||
          (params.source && params.target && findConnectedPair(params.target) === params.source);

        const newEdges = addEdge(
          {
            ...params,
            markerEnd: { type: MarkerType.ArrowClosed },
            animated: !isStepResponseConnection, // Solid for step-response, animated for others
            style: isStepResponseConnection ? {
              stroke: '#000000',
              strokeWidth: 2
            } : undefined,
            type: isStepResponseConnection ? 'step-response' : 'default'
          },
          eds
        );
        // Save to history for undo/redo
        saveEdgeState(newEdges);
        return newEdges;
      });
    },
    [setEdges, saveEdgeState, findConnectedPair]
  );





  const addNode = useCallback((channel) => {
    const id = `${channel}-${Date.now()}`;
    const responseId = `${channel}-response-${Date.now()}`;

    // Find the start node to position new nodes after it
    const startNode = nodes.find(node => node.id === 'start');
    const startX = startNode ? startNode.position.x : 100;
    const startY = startNode ? startNode.position.y : 100;

    // Find existing nodes connected to start to determine positioning
    const existingConnections = edges.filter(edge => edge.source === 'start');
    const existingSteps = nodes.filter(node =>
      existingConnections.some(edge => edge.target === node.id) &&
      !node.data.label?.toLowerCase().includes('response')
    );

    // Calculate position for new nodes
    let stepX, stepY;
    if (existingSteps.length === 0) {
      // First step - position directly below start
      stepX = startX;
      stepY = startY + 150;
    } else {
      // Position parallel to existing steps (same Y level as the first step)
      const firstStep = existingSteps[0];
      stepY = firstStep.position.y;
      stepX = startX + (existingSteps.length * 450); // 450px spacing for parallel steps
    }

    const responseY = stepY + 100; // 100px below the step

    // Determine order for parallel steps
    const currentOrder = existingSteps.length > 0 ? existingSteps[0].data.order : 1;
    const responseOrder = currentOrder + 0.1;

    const newStep = {
      id,
      position: { x: stepX, y: stepY },
      data: {
        label: `${channel} Step`,
        channel,
        delay: 0,
        condition: '',
        queue: 100,
        order: currentOrder, // Same order as other parallel steps
        nodeId: id,
        onDelete: deleteNode,
        onEdit: handleEditStep,
        onAddFollowUp: handleAddFollowUp,
      },
      type: 'custom',
    };

    const responseStep = {
      id: responseId,
      position: { x: stepX, y: responseY },
      data: {
        label: `${channel} Response`,
        channel: `${channel}_response`,
        delay: 0,
        condition: '',
        queue: 100,
        order: responseOrder, // Slightly higher order than the step
        nodeId: responseId,
        onDelete: deleteNode,
        onEdit: handleEditStep,
        onAddFollowUp: handleAddFollowUp,
      },
      type: 'custom',
    };

    setNodes((nds) => [...nds, newStep, responseStep]);

    setEdges((eds) => {
      // Only add step-response connection, no auto-connection to start
      const newEdges = addEdge(
        {
          id: `e-${id}-${responseId}`,
          source: id,
          target: responseId,
          markerEnd: { type: MarkerType.ArrowClosed },
          animated: false, // Solid line for step-response connection
          style: {
            stroke: '#000000', // Black color
            strokeWidth: 2 // Slightly thicker
          },
          type: 'step-response' // Custom type to identify step-response connections
        },
        eds
      );

      // Save to history for undo/redo
      saveEdgeState(newEdges);
      return newEdges;
    });
  }, [nodes, edges, setNodes, setEdges, saveEdgeState, deleteNode, handleEditStep]);



  // const findNearbyNode = (targetNode) => {
  //   const { x: tx, y: ty } = targetNode.position;
  //   return nodes.find((node) => {
  //     if (node.id === targetNode.id) return false;
  //     const dx = node.position.x - tx;
  //     const dy = node.position.y - ty;
  //     return Math.sqrt(dx * dx + dy * dy) <= PROXIMITY_RADIUS;
  //   });
  // };

  const handleNodeClick = useCallback((_event, node) => {
    console.log('Node clicked:', node);
  }, []);



  const saveSequenceToDatabase = useCallback(async () => {
    try {
      if (nodes.length === 0) {
        message.warning('Please add some steps to the sequence before saving.');
        return;
      }

      // Define valid communication types (including _STEP variants)
      const validCommunicationTypes = [
        'EMAIL', 'EMAIL_STEP',
        'SMS', 'SMS_STEP',
        'WHATSAPP', 'WHATSAPP_STEP',
        'CALL', 'CALL_STEP',
        'LI_CONNECTION', 'LI_INMAIL',
        'LINKEDIN', 'LINKEDIN_STEP'
      ];

      // Filter out non-communication nodes (like Start, End, etc.)
      const communicationNodes = nodes.filter(node => {
        const nodeType = node.data.label.toUpperCase().replace(/\s+/g, '_');
        return validCommunicationTypes.includes(nodeType);
      });

      if (communicationNodes.length === 0) {
        message.warning('Please add at least one communication step (Email, SMS, WhatsApp, Call, or LinkedIn) to the sequence.');
        return;
      }

      console.log('All nodes:', nodes.map(n => ({ id: n.id, label: n.data.label })));
      console.log('Filtered communication nodes:', communicationNodes.map(n => ({ id: n.id, label: n.data.label })));

      // Check for duplicate nodes
      const nodeLabels = communicationNodes.map(n => n.data.label);
      const duplicateLabels = nodeLabels.filter((label, index) => nodeLabels.indexOf(label) !== index);
      if (duplicateLabels.length > 0) {
        console.warn('Duplicate node labels found:', duplicateLabels);
      }

      // Group communication nodes by their Y position to determine order
      const nodesByOrder = {};
      communicationNodes.forEach(node => {
        const order = Math.floor(node.position.y / 150); // Group by 150px intervals
        if (!nodesByOrder[order]) {
          nodesByOrder[order] = [];
        }
        nodesByOrder[order].push(node);
      });

      // Sort orders and create sequence steps for database
      const sortedOrders = Object.keys(nodesByOrder).sort((a, b) => parseInt(a) - parseInt(b));
      const sequenceSteps = [];
      const seenSteps = new Set(); // Track unique steps to prevent duplicates

      sortedOrders.forEach((order, orderIndex) => {
        const nodesAtOrder = nodesByOrder[order];

        nodesAtOrder.forEach(node => {
          const nodeLabel = node.data.label.toUpperCase().replace(/\s+/g, '_');

          // Create unique identifier for this step
          const stepKey = `${nodeLabel}_${orderIndex}`;

          if (seenSteps.has(stepKey)) {
            console.warn(`Duplicate step detected: ${stepKey}, skipping`);
            return;
          }

          // Map frontend labels to backend enum values
          const mediumMapping = {
            'EMAIL': 'EMAIL',
            'EMAIL_STEP': 'EMAIL',
            'SMS': 'SMS',
            'SMS_STEP': 'SMS',
            'WHATSAPP': 'WHATSAPP',
            'WHATSAPP_STEP': 'WHATSAPP',
            'CALL': 'CALL',
            'CALL_STEP': 'CALL',
            'LI_CONNECTION': 'LINKEDIN',
            'LI_INMAIL': 'LINKEDIN',
            'LINKEDIN': 'LINKEDIN',
            'LINKEDIN_STEP': 'LINKEDIN'
          };

          const mappedMedium = mediumMapping[nodeLabel];

          if (!mappedMedium) {
            console.warn(`Unknown medium type: ${nodeLabel}, skipping step`);
            return;
          }

          console.log(`Mapping: ${node.id} - ${nodeLabel} → ${mappedMedium} (order: ${orderIndex})`);

          seenSteps.add(stepKey);
          sequenceSteps.push({
            name: node.data.label,
            order: orderIndex, // Use sequential order index instead of Y-position
            type: 'OUTREACH',
            medium: mappedMedium,
            templateId: 1, // Default template
          });
        });
      });

      console.log('Final sequence steps to create:', sequenceSteps);

      // Final validation - check for any remaining duplicates
      const stepNames = sequenceSteps.map(s => s.name);
      const duplicateStepNames = stepNames.filter((name, index) => stepNames.indexOf(name) !== index);
      if (duplicateStepNames.length > 0) {
        console.error('Duplicate steps detected:', duplicateStepNames);
        message.error('Duplicate steps detected. Please remove duplicate steps and try again.');
        return;
      }

      // Import the API functions dynamically
      const { createSequence, createSequenceStep } = await import('../../../../../api/sequenceExecution');

      // Get roleId from useParams
      const roleId = paramRoleId;

      console.log('Current URL:', window.location.pathname);
      console.log('useParams roleId:', paramRoleId);
      console.log('Using roleId:', roleId);

      // Validate roleId
      if (!roleId) {
        throw new Error('Role ID not found. Please ensure you are on a valid role page.');
      }

      console.log('Using role ID from useParams:', roleId);

      // Get the real user ID from Redux state
      const userId = user?.id || user?.userId || user?.user_id;

      if (!userId) {
        throw new Error('User not found. Please ensure you are logged in.');
      }

      console.log('Using user ID from Redux:', userId);

      // Create main sequence data
      const sequenceData = {
        name: sequenceName || `Sequence for Role ${roleId}`,
        description: 'Created from Advanced Sequence Builder',
        status: 'ACTIVE',
        userId: userId, // Use real user ID from Redux
        roleId: parseInt(roleId)
      };

      console.log('=== SEQUENCE CREATION DEBUG ===');
      console.log('Component: AdvanceSequence.jsx');
      console.log('Role ID from useParams:', paramRoleId);
      console.log('User from Redux:', user);
      console.log('Final userId being used:', userId);
      console.log('Sequence data to be sent:', sequenceData);
      console.log('Steps to be created:', sequenceSteps);

      // Create sequence first
      const sequence = await createSequence(sequenceData);
      console.log('Created sequence response:', sequence);

      // Create sequence steps and their follow-ups
      const createdSteps = [];
      const stepFollowUpMap = new Map(); // Track which steps have follow-ups

      // First pass: Create main steps and collect follow-up data
      for (const step of sequenceSteps) {
        const stepWithSequenceId = {
          ...step,
          roleSequenceId: sequence.id
        };

        // Check if this step has follow-up configuration from node data
        const nodeData = nodes.find(node => node.data.label === step.name);
        if (nodeData?.data?.hasFollowUp && nodeData?.data?.followUpSteps) {
          stepFollowUpMap.set(step.name, nodeData.data.followUpSteps);
          stepWithSequenceId.hasNoResponseFollowUp = true;
          stepWithSequenceId.noResponseDelayHours = nodeData.data.followUpSteps[0]?.delayHours || 24;
        }

        const createdStep = await createSequenceStep(stepWithSequenceId);
        createdSteps.push(createdStep);
      }

      // Second pass: Create follow-up steps
      for (const createdStep of createdSteps) {
        const followUpSteps = stepFollowUpMap.get(createdStep.name);
        if (followUpSteps && followUpSteps.length > 0) {
          console.log(`Creating ${followUpSteps.length} follow-up steps for: ${createdStep.name}`);

          for (const followUpStep of followUpSteps) {
            const followUpStepData = {
              name: followUpStep.name || `${createdStep.name} Follow-up`,
              order: createdStep.order + 0.1, // Slightly higher order
              type: 'NO_RESPONSE_FOLLOW_UP',
              medium: followUpStep.medium,
              templateId: followUpStep.templateId || 1,
              roleSequenceId: sequence.id,
              parentStepId: createdStep.id,
              noResponseDelayHours: followUpStep.delayHours,
              followUpTrigger: 'DELAYED'
            };

            const createdFollowUpStep = await createSequenceStep(followUpStepData);
            console.log(`Created follow-up step: ${createdFollowUpStep.name}`);
          }
        }
      }

      console.log('=== SEQUENCE WITH STEPS SAVED TO DATABASE ===');
      console.log('Sequence ID:', sequence.id);
      console.log('Total Steps:', sequenceSteps.length);
      console.log('Steps created:', createdSteps.length);

      // Use message instead of alert
      message.success(`Sequence "${sequenceData.name}" with ${sequenceSteps.length} steps saved successfully! You can now execute it from the Sequence Execution Dashboard.`);

      return { sequence, steps: createdSteps };
    } catch (error) {
      console.error('Error saving sequence:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        response: error.response
      });

      let errorMessage = 'Failed to save sequence. ';
      if (error.response) {
        errorMessage += `Server responded with: ${error.response.status} ${error.response.statusText}`;
      } else if (error.message) {
        errorMessage += error.message;
      } else {
        errorMessage += 'Please ensure the backend is running and try again.';
      }

      message.error(errorMessage);
      throw error;
    }
  }, [nodes, sequenceName]);

  const exportSequence = useCallback(() => {
    // Group nodes by order for parallel steps
    const nodeGroups = {};
    nodes.forEach(node => {
      const order = node.data.order ?? 0;
      if (!nodeGroups[order]) {
        nodeGroups[order] = [];
      }
      nodeGroups[order].push(node);
    });

    // Sort groups by order and create sequence structure
    const sortedOrders = Object.keys(nodeGroups).sort((a, b) => parseFloat(a) - parseFloat(b));
    const sequenceSteps = sortedOrders.map(order => {
      const group = nodeGroups[order];
      if (group.length === 1) {
        return {
          order: parseFloat(order),
          type: 'sequential',
          step: group[0]
        };
      } else {
        return {
          order: parseFloat(order),
          type: 'parallel',
          steps: group
        };
      }
    });

    const sequence = {
      sequenceSteps,
      edges: edges,
      totalSteps: nodes.length,
      parallelGroups: sortedOrders.filter(order => nodeGroups[order].length > 1).length
    };

    console.log('=== SEQUENCE EXPORT ===');
    console.log('Sequence Steps Order:');
    sequenceSteps.forEach((step, index) => {
      if (step.type === 'sequential') {
        console.log(`${index + 1}. [Sequential] Order ${step.order}: ${step.step.data.label}`);
      } else {
        console.log(`${index + 1}. [Parallel] Order ${step.order}:`);
        step.steps.forEach((parallelStep, pIndex) => {
          console.log(`   ${pIndex + 1}. ${parallelStep.data.label}`);
        });
      }
    });
    console.log('Full Sequence JSON:', JSON.stringify(sequence, null, 2));
  }, [nodes, edges]);



  return (
    <div style={{ height: '100vh', width: '100%' }}>
      <ReactFlow
        nodeTypes={nodeTypes}
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeClick={handleNodeClick}
        fitView
      >

        <Background variant="none" />
        <MiniMap />
        <Controls />

        <Panel position="top-left">
          <div
            style={{
              background: '#fff',
              padding: 10,
              borderRadius: 8,
              boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
            }}
          >
            {/* Undo/Redo Controls */}
            <Space style={{ marginBottom: 8 }}>
              <Tooltip title="Undo connection (Ctrl+Z)" placement="bottom">
                <Button
                  icon={<UndoOutlined />}
                  onClick={undoEdgeChange}
                  disabled={!canUndo}
                  size="small"
                >
                  Undo
                </Button>
              </Tooltip>
              <Tooltip title="Redo connection (Ctrl+Y)" placement="bottom">
                <Button
                  icon={<RedoOutlined />}
                  onClick={redoEdgeChange}
                  disabled={!canRedo}
                  size="small"
                >
                  Redo
                </Button>
              </Tooltip>
            </Space>

            <Divider style={{ margin: '8px 0' }} />

            {/* Add Node Controls */}
            <div>
              <Button onClick={() => addNode('Email')} style={{ marginRight: 4, marginBottom: 4 }}>
                Add Email
              </Button>
              <Button onClick={() => addNode('WhatsApp')} style={{ marginRight: 4, marginBottom: 4 }}>
                Add WhatsApp
              </Button>
              <Button onClick={() => addNode('SMS')} style={{ marginRight: 4, marginBottom: 4 }}>
                Add SMS
              </Button>
              <Button onClick={() => addNode('Call')} style={{ marginRight: 4, marginBottom: 4 }}>
                Add Call
              </Button>
              <Button onClick={() => addNode('LI Connection')} style={{ marginRight: 4, marginBottom: 4 }}>
                LI Connection
              </Button>
              <Button onClick={() => addNode('LI InMail')} style={{ marginRight: 4, marginBottom: 4 }}>
                LI InMail
              </Button>
              <Button onClick={exportSequence} type="dashed" style={{ marginTop: 4 }}>
                Export
              </Button>
            </div>
          </div>
        </Panel>
        <Panel position="top-right" style={{ backgroundColor: '#f2f5fa', width: '500px' }}>
          <div
            style={{
              background: '#fff',
              padding: 10,
              borderRadius: 8,
              boxShadow: '0 2px 6px rgba(0,0,0,0.1)',
              display: 'flex',
              alignItems: 'center'
              // flexDirection: 'column'
            }}
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}>
              <Input
                placeholder="Enter sequence name"
                onChange={(e) => setSequenceName(e.target.value)}
                value={sequenceName}
                style={{ padding: '5px', border: '1px solid #ced0da' }}
                required
              />
              <div style={{ fontSize: '12px', color: '#666', padding: '2px 5px' }}>
                Role ID: {paramRoleId || 'Loading...'}
              </div>
            </div>
            <Button onClick={saveSequenceToDatabase} style={{ marginTop: 4, backgroundColor: '#1A84DE', color: 'white', width: '160px', height: '35px', marginLeft:'10px' }}>
              Save to Database
            </Button>
            <Button onClick={exportSequence} style={{ marginTop: 4, backgroundColor: '#1A84DE', color: 'white', width: '160px', height: '35px', marginLeft: '10px' }}>
              Save Sequence
            </Button>

          </div>
        </Panel>

      </ReactFlow>

      {/* Edit Sequence Template Dialog */}
      <EditSequenceTemplateDialog
        open={editDialogOpen}
        onClose={handleDialogClose}
        step={editingStep}
      />

      {/* Follow-up Step Manager Dialog */}
      <FollowUpStepManager
        visible={followUpDialogOpen}
        onClose={handleFollowUpDialogClose}
        stepId={selectedStepForFollowUp?.nodeId}
        stepName={selectedStepForFollowUp?.label}
        onFollowUpCreated={handleFollowUpCreated}
      />
    </div>
  );
};

export default SequenceBuilder;
