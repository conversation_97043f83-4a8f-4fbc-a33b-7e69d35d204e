import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Cron, CronExpression } from '@nestjs/schedule';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { CandidateSequenceStatus, SequenceStepStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';
import { CandidateSequenceStatusService } from 'src/candidate-sequence-status/candidate-sequence-status.service';
import { QueueService } from 'src/queue/queue.service';

@Injectable()
export class FollowUpService {
  private readonly logger = new Logger(FollowUpService.name);

  constructor(
    @InjectRepository(SequenceSteps)
    private readonly sequenceStepsRepository: Repository<SequenceSteps>,
    @InjectRepository(CandidateSequenceStatus)
    private readonly candidateSequenceStatusRepository: Repository<CandidateSequenceStatus>,
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
    private readonly queueService: QueueService,
  ) {}

  /**
   * Cron job to check for steps that need follow-up due to no response
   * Runs every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async checkForNoResponseFollowUps(): Promise<void> {
    this.logger.log('Checking for no-response follow-ups...');

    try {
      // Find all steps that are sent/delivered but not replied/completed and have follow-up configured
      const candidatesNeedingFollowUp = await this.candidateSequenceStatusRepository
        .createQueryBuilder('css')
        .innerJoinAndSelect('css.step', 'step')
        .where('css.status IN (:...statuses)', { 
          statuses: [SequenceStepStatus.SENT, SequenceStepStatus.DELIVERED, SequenceStepStatus.OPENED] 
        })
        .andWhere('step.hasNoResponseFollowUp = :hasFollowUp', { hasFollowUp: true })
        .andWhere('step.noResponseDelayHours IS NOT NULL')
        .andWhere('css.sentAt IS NOT NULL')
        .andWhere('css.repliedAt IS NULL')
        .andWhere('css.completedAt IS NULL')
        .andWhere('css.sentAt <= :cutoffTime', { 
          cutoffTime: new Date(Date.now() - 1000 * 60 * 60) // 1 hour ago minimum
        })
        .getMany();

      this.logger.log(`Found ${candidatesNeedingFollowUp.length} candidates needing follow-up`);

      for (const candidateStatus of candidatesNeedingFollowUp) {
        await this.processNoResponseFollowUp(candidateStatus);
      }

    } catch (error) {
      this.logger.error('Error checking for no-response follow-ups:', error);
    }
  }

  /**
   * Process a single candidate's no-response follow-up
   */
  private async processNoResponseFollowUp(candidateStatus: CandidateSequenceStatus): Promise<void> {
    try {
      const step = candidateStatus.step;
      const delayHours = step.noResponseDelayHours || 24; // Default 24 hours
      const cutoffTime = new Date(Date.now() - delayHours * 60 * 60 * 1000);

      // Check if enough time has passed since the step was sent
      if (!candidateStatus.sentAt || candidateStatus.sentAt > cutoffTime) {
        return; // Not enough time has passed
      }

      // Check if follow-up has already been triggered
      const existingFollowUp = await this.candidateSequenceStatusRepository.findOne({
        where: {
          candidateId: candidateStatus.candidateId,
          sequenceId: candidateStatus.sequenceId,
          step: { parentStepId: step.id }
        }
      });

      if (existingFollowUp) {
        this.logger.debug(`Follow-up already exists for candidate ${candidateStatus.candidateId}, step ${step.id}`);
        return;
      }

      // Get follow-up steps for this step
      const followUpSteps = await this.sequenceStepsRepository.find({
        where: { parentStepId: step.id },
        order: { order: 'ASC' }
      });

      if (followUpSteps.length === 0) {
        this.logger.warn(`No follow-up steps configured for step ${step.id}`);
        return;
      }

      this.logger.log(`Triggering ${followUpSteps.length} follow-up steps for candidate ${candidateStatus.candidateId}, original step ${step.id}`);

      // Create and execute follow-up steps
      for (const followUpStep of followUpSteps) {
        await this.createAndExecuteFollowUpStep(candidateStatus, followUpStep);
      }

      // Mark original step as having follow-up triggered
      await this.candidateSequenceStatusService.updateStatus(
        candidateStatus.id,
        candidateStatus.status,
        { followUpTriggered: true, followUpTriggeredAt: new Date().toISOString() }
      );

    } catch (error) {
      this.logger.error(`Error processing follow-up for candidate ${candidateStatus.candidateId}:`, error);
    }
  }

   async
  /**
   * Create and execute a follow-up step
   */
  private async createAndExecuteFollowUpStep(
    originalCandidateStatus: CandidateSequenceStatus,
    followUpStep: SequenceSteps
  ): Promise<void> {
    try {
      // Create candidate sequence status for the follow-up step
      const followUpStatus = await this.candidateSequenceStatusService.createCandidateSequenceStatus(
        originalCandidateStatus.candidateId,
        originalCandidateStatus.sequenceId,
        followUpStep.id,
        originalCandidateStatus.stepType,
        followUpStep.order
      );

      // Queue the follow-up step for execution
      const queueJobData = {
        candidateSequenceStatusId: followUpStatus.id,
        candidateId: originalCandidateStatus.candidateId,
        stepId: followUpStep.id,
        sequenceId: originalCandidateStatus.sequenceId,
        medium: followUpStep.medium,
        templateId: followUpStep.templateId,
        isFollowUp: true,
        originalStepId: originalCandidateStatus.stepId
      };

      await this.queueService.addJobToQueue(followUpStep.medium.toLowerCase(), queueJobData);

      this.logger.log(`Follow-up step ${followUpStep.id} queued for candidate ${originalCandidateStatus.candidateId}`);

    } catch (error) {
      this.logger.error(`Error creating follow-up step for candidate ${originalCandidateStatus.candidateId}:`, error);
    }
  }

  /**
   * Manually trigger follow-up for a specific step
   */
  async triggerManualFollowUp(candidateId: number, stepId: number): Promise<void> {
    try {
      const candidateStatus = await this.candidateSequenceStatusRepository.findOne({
        where: { candidateId, stepId },
        relations: ['step']
      });

      if (!candidateStatus) {
        throw new Error(`Candidate sequence status not found for candidate ${candidateId}, step ${stepId}`);
      }

      await this.processNoResponseFollowUp(candidateStatus);
      this.logger.log(`Manual follow-up triggered for candidate ${candidateId}, step ${stepId}`);

    } catch (error) {
      this.logger.error(`Error triggering manual follow-up:`, error);
      throw error;
    }
  }

  /**
   * Create follow-up steps for a sequence step
   */
  async createFollowUpSteps(
    parentStepId: number,
    followUpStepsData: Array<{
      name: string;
      type: string;
      medium: string;
      templateId?: number;
      order: number;
      noResponseDelayHours?: number;
    }>
  ): Promise<SequenceSteps[]> {
    try {
      const parentStep = await this.sequenceStepsRepository.findOne({
        where: { id: parentStepId }
      });

      if (!parentStep) {
        throw new Error(`Parent step ${parentStepId} not found`);
      }

      const followUpSteps: SequenceSteps[] = [];

      for (const stepData of followUpStepsData) {
        const followUpStep = this.sequenceStepsRepository.create({
          ...stepData,
          type: 'NO_RESPONSE_FOLLOW_UP',
          parentStepId: parentStepId,
          roleSequenceId: parentStep.roleSequenceId,
          followUpTrigger: 'DELAYED'
        });

        const savedStep = await this.sequenceStepsRepository.save(followUpStep);
        followUpSteps.push(savedStep);
      }

      // Update parent step to indicate it has follow-up
      await this.sequenceStepsRepository.update(parentStepId, {
        hasNoResponseFollowUp: true,
        noResponseDelayHours: followUpStepsData[0]?.noResponseDelayHours || 24
      });

      this.logger.log(`Created ${followUpSteps.length} follow-up steps for parent step ${parentStepId}`);
      return followUpSteps;

    } catch (error) {
      this.logger.error(`Error creating follow-up steps:`, error);
      throw error;
    }
  }

  /**
   * Get follow-up steps for a parent step
   */
  async getFollowUpSteps(parentStepId: number): Promise<SequenceSteps[]> {
    return await this.sequenceStepsRepository.find({
      where: { parentStepId },
      order: { order: 'ASC' }
    });
  }

  /**
   * Check if a candidate has responded to any step in the sequence
   */
  async hasRespondedToSequence(candidateId: number, sequenceId: number): Promise<boolean> {
    const responseCount = await this.candidateSequenceStatusRepository.count({
      where: {
        candidateId,
        sequenceId,
        status: SequenceStepStatus.REPLIED
      }
    });

    return responseCount > 0;
  }
}
