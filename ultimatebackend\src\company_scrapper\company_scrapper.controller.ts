import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { CompanyScrapperService } from './company_scrapper.service';
import { CreateCompanyScrapperDto } from './dto/create-company_scrapper.dto';
import { UpdateCompanyScrapperDto } from './dto/update-company_scrapper.dto';

@Controller('company-scrapper')
export class CompanyScrapperController {
  constructor(private readonly companyScrapperService: CompanyScrapperService) {}

  @Post()
  create(@Body() createCompanyScrapperDto: CreateCompanyScrapperDto) {
    return this.companyScrapperService.create(createCompanyScrapperDto);
  }

  @Get()
  findAll() {
    return this.companyScrapperService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.companyScrapperService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCompanyScrapperDto: UpdateCompanyScrapperDto) {
    return this.companyScrapperService.update(+id, updateCompanyScrapperDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.companyScrapperService.remove(+id);
  }
}
