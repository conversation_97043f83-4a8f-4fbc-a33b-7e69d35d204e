import { StripeService } from './../stripe/stripe.service';
import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Invoices } from './invoices.entity';
import { InvoiceDto, UpdateInvoiceDto } from './dto/invoices.dto';
import { AlreadyExistsException } from '@aws-sdk/client-ses';
import { Repository } from 'typeorm';
import { Service } from 'src/service/service.entity';
import { Users } from 'src/users/users.entity';
import { People } from 'src/people/people.entity';
import * as moment from 'moment';

@Injectable()
export class InvoicesService {
  constructor(
    @InjectRepository(Invoices)
    private readonly invoicesRepository: Repository<Invoices>,
    @InjectRepository(Service)
    private readonly serviceRepository: Repository<Service>,
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    private stripeService: StripeService,
  ) {}

  async createInvoice(invoiceDto: InvoiceDto): Promise<Invoices> {
    try {
      const service = await this.serviceRepository.findOne({
        where: { id: invoiceDto.serviceId },
      });

      const prospect = await this.peopleRepository.findOne({
        where: { id: invoiceDto.personId },
      });

      const successUrl = 'https://www.google.com';
      const cancelUrl = 'https://www.fb.com';

      const session = await this.stripeService.createCheckoutSession({
        amount: invoiceDto.amount,
        currency: invoiceDto.currency,
        service: service.name,
        prospect: prospect.full_name,
        invoiceNumber: invoiceDto.invoice_number.toString(),
        invoiceDate: invoiceDto.invoice_date.toString(),
        dueDate: invoiceDto.due_date.toString(),
        successUrl: successUrl,
        cancelUrl: cancelUrl,
      });
      if (!session || !session.id || !session.url) {
        throw new BadRequestException(
          'Failed to create Stripe checkout session',
        );
      }
      const invoice = this.invoicesRepository.create({
        ...invoiceDto,
        session_id: session.id,
        session_url: session.url,
      });
      return await this.invoicesRepository.save(invoice);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error creating invoice',
        error: error.message,
      });
    }
  }

  async updateInvoice(
    id: number,
    invoice: UpdateInvoiceDto,
  ): Promise<Invoices> {
    try {
      const invoiceToUpdate = await this.invoicesRepository.findOne({
        where: { id },
      });
      if (!invoiceToUpdate) {
        throw new NotFoundException('Invoice not found');
      }
      Object.assign(invoiceToUpdate, invoice);
      return await this.invoicesRepository.save(invoiceToUpdate);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error updating invoice',
        error: error.message,
      });
    }
  }

  async deleteInvoice(id: number): Promise<void> {
    try {
      const invoiceToDelete = await this.invoicesRepository.findOne({
        where: { id },
      });
      if (!invoiceToDelete) {
        throw new NotFoundException('Invoice not found');
      }
      await this.invoicesRepository.remove(invoiceToDelete);
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error deleting invoice',
        error: error.message,
      });
    }
  }

  async getInvoiceById(id: number): Promise<Invoices> {
    try {
      const invoice = await this.invoicesRepository.findOne({
        where: { id },
      });
      if (!invoice) {
        throw new NotFoundException('Invoice not found');
      }
      return invoice;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching invoice',
        error: error.message,
      });
    }
  }

  async getAllInvoices(
    page: number = 0,
    pageSize = 10,
    searchString?: string,
    start_date?: Date,
    end_date?: Date,
    status?: string,
  ): Promise<Invoices[]> {
    const skip = page * pageSize;
    const limit = pageSize;

    try {
      const query = this.invoicesRepository.createQueryBuilder('invoice');
      query.leftJoin('invoice.user', 'user');
      query.addSelect(['user.first_name', 'user.last_name']);
      query.leftJoinAndSelect('invoice.person', 'person');
      query.leftJoin('person.company', 'company');
      query.addSelect(['company.name']);

      if (searchString) {
        query.andWhere('invoice.invoice_number LIKE :searchString', {
          searchString: `%${searchString}%`,
        });
      }

      if (start_date) {
        query.andWhere('invoice.invoice_date >= :start_date', { start_date });
      }

      if (end_date) {
        query.andWhere('invoice.invoice_date <= :end_date', { end_date });
      }

      if (status) {
        query.andWhere('invoice.status = :status', { status });
      }

      query.orderBy('invoice.invoice_date', 'DESC');
      query.skip(skip).take(limit);

      return await query.getMany();
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching invoices',
        error: error.message,
      });
    }
  }

  async getAllInvoicesStatGroupByStatus(): Promise<
    { status: string; count: number }[]
  > {
    try {
      const query = this.invoicesRepository
        .createQueryBuilder('invoice')
        .select('invoice.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .groupBy('invoice.status');

      return await query.getRawMany();
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching invoices stats',
        error: error.message,
      });
    }
  }

  async getInvoicesForBdDashboard() {
    try {
      const today = new Date();
      const threeMonthsAgo = new Date();
      threeMonthsAgo.setMonth(today.getMonth() - 2);

      const invoices = await this.invoicesRepository
        .createQueryBuilder('invoice')
        .where('invoice.invoice_date BETWEEN :start AND :end', {
          start: moment(threeMonthsAgo).startOf('month').format('YYYY-MM-DD'),
          end: moment(today).endOf('month').format('YYYY-MM-DD'),
        })
        .getMany();

      const statusColorMap = {
        PAID: '#00e272',
        UNPAID: '#fcb92c',
        INVOICED: '#2caffe',
        CANCELLED: '#fe6a35',
      };

      const getMonthName = (date: Date) => moment(date).format('MMMM');

      const grouped = {};

      invoices.forEach((invoice) => {
        const month = getMonthName(invoice.invoice_date);
        if (!grouped[month]) grouped[month] = {};
        if (!grouped[month][invoice.status]) grouped[month][invoice.status] = 0;
        grouped[month][invoice.status]++;
      });

      const result = Object.entries(grouped).map(([month, statuses]) => ({
        month,
        data: Object.entries(statuses).map(([status, count]) => ({
          name: status,
          count,
          color: statusColorMap[status] || 'gray',
        })),
      }));

      return result;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Error fetching invoices stats',
        error: error,
      });
    }
  }
}
