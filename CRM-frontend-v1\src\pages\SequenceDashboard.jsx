import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Progress, 
  Statistic, 
  Table, 
  Button, 
  Tag, 
  Space, 
  Timeline, 
  Tabs,
  Select,
  DatePicker,
  message,
  Modal,
  Tooltip,
  Badge
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  EyeOutlined,
  Bar<PERSON>hartOutlined,
  UserOutlined,
  MailOutlined,
  MessageOutlined,
  PhoneOutlined,
  LinkedinOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { Get, Post } from '../api/api';
import { sequenceAnalyticsAPI, sequenceManagementAPI, dashboardAPI } from '../api/sequenceAnalytics';
import CandidateJourneyView from '../components/CandidateJourneyView';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;

const SequenceDashboard = () => {
  const [sequences, setSequences] = useState([]);
  const [selectedSequence, setSelectedSequence] = useState(null);
  const [loading, setLoading] = useState(false);
  const [analytics, setAnalytics] = useState({});
  const [candidateJourneys, setCandidateJourneys] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedCandidate, setSelectedCandidate] = useState(null);
  const [journeyModalVisible, setJourneyModalVisible] = useState(false);
  const [dashboardData, setDashboardData] = useState({});

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await dashboardAPI.getDashboardOverview();
      setDashboardData(data);
      setSequences(data.sequences);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      // Fallback to individual API calls
      loadSequences();
    } finally {
      setLoading(false);
    }
  };

  const loadSequences = async () => {
    try {
      setLoading(true);
      const response = await Get('http://localhost:5001/api/sequence/get-all');
      const sequencesData = response.data || response;
      setSequences(sequencesData);
      
      // Load analytics for each sequence
      for (const sequence of sequencesData) {
        await loadSequenceAnalytics(sequence.id);
      }
    } catch (error) {
      message.error('Failed to load sequences');
      console.error('Error loading sequences:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSequenceAnalytics = async (sequenceId) => {
    try {
      const response = await Get(`http://localhost:5001/api/sequence/${sequenceId}/stats`);
      setAnalytics(prev => ({
        ...prev,
        [sequenceId]: response
      }));
    } catch (error) {
      console.error(`Error loading analytics for sequence ${sequenceId}:`, error);
    }
  };

  const loadCandidateJourneys = async (sequenceId) => {
    try {
      const response = await Get(`http://localhost:5001/api/candidate-sequence-status/sequence/${sequenceId}`);
      setCandidateJourneys(response);
    } catch (error) {
      console.error('Error loading candidate journeys:', error);
    }
  };

  const handleSequenceAction = async (sequenceId, action) => {
    try {
      setLoading(true);

      switch (action) {
        case 'start':
          await sequenceManagementAPI.startSequence(sequenceId);
          break;
        case 'pause':
          await sequenceManagementAPI.pauseSequence(sequenceId);
          break;
        case 'stop':
          await sequenceManagementAPI.stopSequence(sequenceId);
          break;
        case 'resume':
          await sequenceManagementAPI.resumeSequence(sequenceId);
          break;
        default:
          return;
      }

      message.success(`Sequence ${action}ed successfully`);
      loadDashboardData();
    } catch (error) {
      message.error(`Failed to ${action} sequence`);
      console.error(`Error ${action}ing sequence:`, error);
    } finally {
      setLoading(false);
    }
  };

  const showCandidateJourney = (candidateId, sequenceId) => {
    setSelectedCandidate({ candidateId, sequenceId });
    setJourneyModalVisible(true);
  };

  const getStatusColor = (status) => {
    const colors = {
      'ACTIVE': 'green',
      'PAUSED': 'orange',
      'STOPPED': 'red',
      'DRAFT': 'blue',
      'COMPLETED': 'purple'
    };
    return colors[status] || 'default';
  };

  const getStepIcon = (medium) => {
    const icons = {
      'EMAIL': <MailOutlined />,
      'SMS': <MessageOutlined />,
      'WHATSAPP': <MessageOutlined />,
      'LINKEDIN': <LinkedinOutlined />,
      'CALL': <PhoneOutlined />
    };
    return icons[medium] || <MailOutlined />;
  };

  const renderDashboardSummary = () => {
    const summary = dashboardData.summary || {};

    return (
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Sequences"
              value={summary.totalSequences || 0}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Active Sequences"
              value={summary.activeSequences || 0}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Candidates"
              value={summary.totalCandidates || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Responses"
              value={summary.totalResponses || 0}
              prefix={<MailOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  const renderSequenceOverview = () => (
    <div>
      {renderDashboardSummary()}
      <Row gutter={[16, 16]}>
        {sequences.map(sequence => {
          const stats = sequence.stats || analytics[sequence.id] || {};
          const totalCandidates = stats.total || 0;
          const completedCandidates = stats.completed || 0;
          const successRate = totalCandidates > 0 ? ((completedCandidates / totalCandidates) * 100).toFixed(1) : 0;

        return (
          <Col xs={24} sm={12} lg={8} key={sequence.id}>
            <Card
              title={
                <Space>
                  <span>{sequence.name}</span>
                  <Tag color={getStatusColor(sequence.status)}>{sequence.status}</Tag>
                  {stats.total > 0 && (
                    <Badge count={stats.total} style={{ backgroundColor: '#52c41a' }} />
                  )}
                </Space>
              }
              extra={
                <Space>
                  <Tooltip title="View Details">
                    <Button
                      icon={<EyeOutlined />}
                      size="small"
                      onClick={() => {
                        setSelectedSequence(sequence);
                        setActiveTab('details');
                        loadCandidateJourneys(sequence.id);
                      }}
                    />
                  </Tooltip>
                  {sequence.status === 'ACTIVE' ? (
                    <Tooltip title="Pause Sequence">
                      <Button
                        icon={<PauseCircleOutlined />}
                        size="small"
                        onClick={() => handleSequenceAction(sequence.id, 'pause')}
                      />
                    </Tooltip>
                  ) : (
                    <Tooltip title="Start Sequence">
                      <Button
                        icon={<PlayCircleOutlined />}
                        type="primary"
                        size="small"
                        onClick={() => handleSequenceAction(sequence.id, 'start')}
                      />
                    </Tooltip>
                  )}
                  <Tooltip title="Stop Sequence">
                    <Button
                      icon={<StopOutlined />}
                      danger
                      size="small"
                      onClick={() => handleSequenceAction(sequence.id, 'stop')}
                      disabled={sequence.status === 'STOPPED'}
                    />
                  </Tooltip>
                </Space>
              }
              hoverable
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="Total Candidates"
                    value={totalCandidates}
                    prefix={<UserOutlined />}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="Success Rate"
                    value={successRate}
                    suffix="%"
                    valueStyle={{ color: successRate > 20 ? '#3f8600' : '#cf1322' }}
                  />
                </Col>
              </Row>
              
              <div style={{ marginTop: 16 }}>
                <div style={{ marginBottom: 8 }}>
                  <span>Progress: </span>
                  <span style={{ float: 'right' }}>{completedCandidates}/{totalCandidates}</span>
                </div>
                <Progress 
                  percent={totalCandidates > 0 ? (completedCandidates / totalCandidates) * 100 : 0}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>

              <div style={{ marginTop: 16 }}>
                <Row gutter={8}>
                  <Col span={6}>
                    <Statistic
                      title="Sent"
                      value={stats.sent || 0}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Opened"
                      value={stats.opened || 0}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Replied"
                      value={stats.replied || 0}
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={6}>
                    <Statistic
                      title="Failed"
                      value={stats.failed || 0}
                      valueStyle={{ fontSize: '14px', color: '#cf1322' }}
                    />
                  </Col>
                </Row>
              </div>
            </Card>
          </Col>
        );
      })}
    </Row>
  );

  const renderSequenceDetails = () => {
    if (!selectedSequence) return <div>Select a sequence to view details</div>;

    const stats = analytics[selectedSequence.id] || {};
    
    return (
      <div>
        <Card title={`${selectedSequence.name} - Detailed Analytics`} style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Total Candidates"
                  value={stats.total || 0}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Response Rate"
                  value={stats.total > 0 ? ((stats.replied || 0) / stats.total * 100).toFixed(1) : 0}
                  suffix="%"
                  prefix={<MailOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Open Rate"
                  value={stats.sent > 0 ? ((stats.opened || 0) / stats.sent * 100).toFixed(1) : 0}
                  suffix="%"
                  prefix={<EyeOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="Failure Rate"
                  value={stats.total > 0 ? ((stats.failed || 0) / stats.total * 100).toFixed(1) : 0}
                  suffix="%"
                  prefix={<ExclamationCircleOutlined />}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Card>
            </Col>
          </Row>
        </Card>

        <Card title="Candidate Journeys" style={{ marginBottom: 16 }}>
          <Table
            dataSource={candidateJourneys}
            columns={[
              {
                title: 'Candidate',
                dataIndex: 'candidateId',
                key: 'candidateId',
                render: (id) => `Candidate ${id}`
              },
              {
                title: 'Current Step',
                dataIndex: 'currentStep',
                key: 'currentStep',
                render: (step) => (
                  <Space>
                    {getStepIcon(step?.medium)}
                    {step?.name || 'Not Started'}
                  </Space>
                )
              },
              {
                title: 'Status',
                dataIndex: 'status',
                key: 'status',
                render: (status) => (
                  <Tag color={status === 'REPLIED' ? 'green' : status === 'FAILED' ? 'red' : 'blue'}>
                    {status}
                  </Tag>
                )
              },
              {
                title: 'Last Activity',
                dataIndex: 'lastActivity',
                key: 'lastActivity',
                render: (date) => date ? new Date(date).toLocaleDateString() : 'N/A'
              },
              {
                title: 'Actions',
                key: 'actions',
                render: (_, record) => (
                  <Space>
                    <Button
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => showCandidateJourney(record.candidateId, selectedSequence.id)}
                    >
                      View Journey
                    </Button>
                    <Button size="small">Retry</Button>
                  </Space>
                )
              }
            ]}
            pagination={{ pageSize: 10 }}
          />
        </Card>
      </div>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: 24 }}>
        <h1>Sequence Dashboard</h1>
        <p>Comprehensive view of all your recruitment sequences with real-time analytics</p>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="Overview" key="overview">
          {renderSequenceOverview()}
        </TabPane>
        
        <TabPane tab="Detailed Analytics" key="details">
          {renderSequenceDetails()}
        </TabPane>
        
        <TabPane tab="Performance Trends" key="trends">
          <Card title="Coming Soon: Performance Trends">
            <p>Historical performance data and trend analysis will be available here.</p>
          </Card>
        </TabPane>
      </Tabs>

      {/* Candidate Journey Modal */}
      <CandidateJourneyView
        candidateId={selectedCandidate?.candidateId}
        sequenceId={selectedCandidate?.sequenceId}
        visible={journeyModalVisible}
        onClose={() => {
          setJourneyModalVisible(false);
          setSelectedCandidate(null);
        }}
      />
    </div>
  );
};

export default SequenceDashboard;
