import { Icon } from '@iconify/react';
import { <PERSON>, Dialog, Di<PERSON><PERSON>ontent, Di<PERSON>T<PERSON>le, Slide, ToggleButton, ToggleButtonGroup, Typography } from '@mui/material';
import { Button, DatePicker, Input, Switch, InputNumber, Select, Card, Space, Divider, Collapse } from 'antd';
import React, { useState } from 'react';
import { TextEditor } from 'sections/bd-section/templates';
import styled from 'styled-components';
import ReactMarkdown from 'react-markdown';
import { ClockCircleOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const StyledInput = styled(Input)`
  padding: 15px;
  margin-top: 20px;
  width: 100%;
  border-radius: 8px;
  background-color: white;
  border: 1px solid #d9d9d9;
`;
const Transition = React.forwardRef(function Transition(props, ref) {
  return (
    <Slide
      direction="left"
      ref={ref}
      {...props}
      timeout={{ enter: 1000, exit: 500 }} // Smooth exit transition
      easing={{
        enter: 'cubic-bezier(0.4, 0, 0.2, 1)',
        exit: 'cubic-bezier(0.4, 0, 0.2, 1)'
      }}
    />
  );
});

const templates = [
  {
    id: 1,
    name: 'Template 1',
    content: 'This is the template for sending email to candidates'
  },
  {
    id: 2,
    name: 'Template 2',
    content: 'This is the template for sending email to candidates'
  }
];

const { Option } = Select;
const { Panel } = Collapse;

function EditSequenceTemplateDialog({ open, onClose, step }) {
  console.log('sdsadtep', step);
  const variables = ['name', 'role', 'salary', 'location'];
  const [alignment, setAlignment] = useState('Preview');
  const [copyMessage, setCopyMessage] = useState(false);
  const [templateContent, setTemplateContent] = useState('');

  // Follow-up configuration state
  const [hasFollowUp, setHasFollowUp] = useState(false);
  const [followUpSteps, setFollowUpSteps] = useState([{
    id: Date.now(),
    name: '',
    medium: 'EMAIL',
    delayHours: 24,
    templateId: 1
  }]);
  const [subject, setSubject] = useState('');
  const [html, setHtml] = useState();
  const [delayMinutes, setDelayMinutes] = useState(step?.delay || 0);
  const handleCopy = () => {
    navigator.clipboard.writeText('{{ name }}').then(() => {
      setCopyMessage(true); // Show message
      setTimeout(() => setCopyMessage(false), 2000); // Hide after 5 seconds
    });
  };

  const handleChange = (event, newAlignment) => {
    if (newAlignment !== null) {
      setAlignment(newAlignment);
    }
  };

  // Follow-up management functions
  const addFollowUpStep = () => {
    const newStep = {
      id: Date.now(),
      name: '',
      medium: 'EMAIL',
      delayHours: 24,
      templateId: 1
    };
    setFollowUpSteps([...followUpSteps, newStep]);
  };

  const removeFollowUpStep = (stepId) => {
    setFollowUpSteps(followUpSteps.filter(step => step.id !== stepId));
  };

  const updateFollowUpStep = (stepId, field, value) => {
    setFollowUpSteps(followUpSteps.map(step =>
      step.id === stepId ? { ...step, [field]: value } : step
    ));
  };

  const handleSave = () => {
    const stepData = {
      delay: delayMinutes,
      subject: subject,
      templateContent: templateContent,
      hasFollowUp: hasFollowUp,
      followUpSteps: hasFollowUp ? followUpSteps : []
    };
    onClose(stepData);
  };

  console.log('templateContent', templateContent);
  return (
    <div>
      <Dialog
        open={open}
        onClose={() => onClose()}
        TransitionComponent={Transition}
        keepMounted // Prevents unmounting, keeping the transition smooth
        fullScreen
        PaperProps={{
          sx: {
            ml: 'auto',
            mt: 0,
            mr: 0,
            width: '1200px',
            height: '100vh',
            overflowY: 'hidden',
            borderRadius: 0
          }
        }}
        BackdropProps={{
          sx: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)'
          }
        }}
      >
        <DialogTitle>
          <Box display="flex" justifyContent="space-between">
            <Typography variant="h4" className="ml-8">
              Message Template Editor
            </Typography>
            <Icon icon="material-symbols:close-rounded" width="24" height="24" style={{ cursor: 'pointer' }} onClick={() => onClose()} />
          </Box>
        </DialogTitle>
        <DialogContent
          sx={{
            width: '100%',
            overflowY: 'auto', // Allow scrolling
            '&::-webkit-scrollbar': {
              width: '5px',
              height: '5px'
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#d9d9d9',
              borderRadius: '4px'
            },
            '&::-webkit-scrollbar-thumb:hover': {
              backgroundColor: '#cccccc'
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#f0f0f0'
            }
          }}
        >
          <div style={{ display: 'flex' }}>
            <div style={{ width: '55%' }}>
              {step?.title?.includes('Email') && (
                <>
                  <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 500, margin: 0 }}>Subject</p>
                  <StyledInput
                    placeholder=""
                    variant="borderless"
                    style={{ width: '100%', marginTop: '5px' }}
                    onChange={(e) => setSubject(e.target.value)}
                    value={subject}
                  />
                </>
              )}
              {step?.title?.includes('Follow up') && (
                <>
                  <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 500, marginTop: '15px' }}>
                    Select Date & Time
                  </p>
                  <DatePicker
                    getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    id="date_picker"
                    showTime
                    readOnly
                    // value={initialValues?.start_date ? dayjs(initialValues?.start_date) : null} // Ensure dayjs object
                    onChange={(date, dateString) => {}}
                    style={{
                      width: '100%',
                      borderRadius: '10px',
                      backgroundColor: 'white',
                      border: '1px solid #ccc',
                      marginTop: 0,
                      padding: '13px 10px',
                      marginBottom: '10px'
                    }}
                    className="ant-picker-custom"
                  />
                </>
              )}
              <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 500 }}>Variables</p>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexDirection: 'row' }}>
                {variables?.map((variable, index) => (
                  <p
                    style={{
                      fontSize: '14px',
                      color: '#1a84de',
                      fontFamily: 'Poppins',
                      fontWeight: 400,
                      backgroundColor: '#edeff8',
                      border: '2px solid #1a84de',
                      borderRadius: '4px',
                      padding: '4px 15px',
                      cursor: 'pointer'
                    }}
                    onClick={handleCopy}
                  >{`{${variable}}`}</p>
                ))}
              </div>
              {copyMessage && (
                <p style={{ fontSize: '14px', color: 'green', fontFamily: 'Poppins', fontWeight: 400, margin: 0 }}>Copied to clipboard!</p>
              )}

              {/* Delay Settings */}
              <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 500, marginTop: '20px' }}>
                Delay Settings
              </p>
              <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '15px' }}>
                <span style={{ fontSize: '14px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>
                  Wait
                </span>
                <StyledInput
                  type="number"
                  placeholder="0"
                  value={delayMinutes}
                  onChange={(e) => setDelayMinutes(parseInt(e.target.value) || 0)}
                  style={{ width: '100px', marginTop: 0 }}
                  min={0}
                />
                <span style={{ fontSize: '14px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>
                  minutes before sending this message
                </span>
              </div>

              <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 500 }}>Message Content</p>
              <TextEditor
                initialContent={templateContent}
                onContentChange={(content) => setTemplateContent(content)}
                onHtmlChange={(html) => setHtml(html)}
                height="400px"
              />

              {/* Follow-up Configuration Section */}
              <Collapse
                style={{ marginTop: '20px' }}
                items={[
                  {
                    key: 'follow-up',
                    label: (
                      <Space>
                        <ClockCircleOutlined />
                        <span style={{ fontWeight: 500 }}>Follow-up Configuration</span>
                        <Switch
                          size="small"
                          checked={hasFollowUp}
                          onChange={setHasFollowUp}
                          onClick={(e) => e.stopPropagation()}
                        />
                      </Space>
                    ),
                    children: hasFollowUp && (
                      <div style={{ padding: '16px 0' }}>
                        <p style={{ marginBottom: '16px', color: '#666' }}>
                          Configure automatic follow-up steps if candidates don't respond to this step.
                        </p>

                        {followUpSteps.map((step, index) => (
                          <Card
                            key={step.id}
                            size="small"
                            style={{ marginBottom: '12px' }}
                            title={`Follow-up Step ${index + 1}`}
                            extra={
                              followUpSteps.length > 1 && (
                                <Button
                                  type="text"
                                  danger
                                  icon={<DeleteOutlined />}
                                  size="small"
                                  onClick={() => removeFollowUpStep(step.id)}
                                />
                              )
                            }
                          >
                            <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                              <div style={{ flex: 1 }}>
                                <Input
                                  placeholder="Follow-up step name"
                                  value={step.name}
                                  onChange={(e) => updateFollowUpStep(step.id, 'name', e.target.value)}
                                />
                              </div>
                              <div style={{ width: '120px' }}>
                                <Select
                                  value={step.medium}
                                  onChange={(value) => updateFollowUpStep(step.id, 'medium', value)}
                                  style={{ width: '100%' }}
                                >
                                  <Option value="EMAIL">📧 Email</Option>
                                  <Option value="SMS">📱 SMS</Option>
                                  <Option value="WHATSAPP">💬 WhatsApp</Option>
                                  <Option value="LINKEDIN">💼 LinkedIn</Option>
                                  <Option value="CALL">📞 Call</Option>
                                </Select>
                              </div>
                              <div style={{ width: '100px' }}>
                                <InputNumber
                                  min={1}
                                  max={168}
                                  value={step.delayHours}
                                  onChange={(value) => updateFollowUpStep(step.id, 'delayHours', value)}
                                  style={{ width: '100%' }}
                                  addonAfter="hrs"
                                />
                              </div>
                            </div>
                          </Card>
                        ))}

                        <Button
                          type="dashed"
                          onClick={addFollowUpStep}
                          icon={<PlusOutlined />}
                          style={{ width: '100%', marginTop: '8px' }}
                        >
                          Add Follow-up Step
                        </Button>
                      </div>
                    )
                  }
                ]}
              />

              <div style={{ display: 'flex', marginTop: '20px' }}>
                <Button
                  type="primary"
                  icon={<Icon icon="material-symbols:close-rounded" width="18" height="18" />}
                  style={{
                    borderRadius: '10px',
                    height: '45px',
                    width: '200px',
                    fontSize: '16px',
                    backgroundColor: '#ff3b30',
                    fontWeight: 500
                  }}
                  onClick={() => onClose()}
                >
                  Close without saving
                </Button>
                <Button
                  type="primary"
                  icon={<Icon icon="mdi:tick" width="18" height="18" />}
                  style={{
                    borderRadius: '10px',
                    height: '45px',
                    width: '150px',
                    fontSize: '16px',
                    marginLeft: '15px',
                    backgroundColor: '#007aff',
                    fontWeight: 500
                  }}
                  onClick={handleSave}
                >
                  Save & Close
                </Button>
              </div>
            </div>
            <div style={{ width: '3%' }}></div>

            <div style={{ width: '42%' }}>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <ToggleButtonGroup
                  value={alignment}
                  exclusive
                  onChange={handleChange}
                  sx={{
                    width: '85%',
                    color: '#fff',
                    margin: 'auto',
                    '& .MuiToggleButtonGroup-grouped': {
                      flex: 1,
                      borderRadius: 0
                    },
                    '& .Mui-selected': {
                      backgroundColor: '#1E88E5 !important', // Blue active color
                      color: '#fff'
                    },
                    '& .MuiToggleButton-root': {
                      backgroundColor: '#2E3B55', // Dark gray inactive color
                      color: '#fff',
                      '&:hover': {
                        backgroundColor: '#3A475E'
                      }
                    }
                  }}
                >
                  <ToggleButton value="Preview">Preview</ToggleButton>
                  <ToggleButton value="Template Gallery">Template Gallery</ToggleButton>
                </ToggleButtonGroup>
              </div>
              {alignment === 'Template Gallery' && (
                <div style={{ padding: '10px 30px' }}>
                  <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>Templates Gallery</p>
                  <p style={{ fontSize: '14px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>
                    Use template you have already created , or create new
                  </p>
                  <Button
                    type="primary"
                    style={{
                      borderRadius: '10px',
                      height: '50px',
                      width: '100%',
                      fontSize: '16px',
                      backgroundColor: '#1A84DE',
                      fontWeight: 500
                    }}
                    onClick={() => {}}
                  >
                    Save Template to Gallery
                  </Button>
                  <StyledInput
                    placeholder="Search templates"
                    value={null}
                    variant="borderless"
                    suffix={<Icon icon="ant-design:search-outlined" width="18" height="18" />}
                  />
                  {templates?.map((template) => (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        backgroundColor: '#f2f5fa',
                        padding: '5px 10px',
                        margin: '15px 0',
                        cursor: 'pointer'
                      }}
                      onClick={() => {
                        setTemplateContent(template?.content);
                      }}
                    >
                      <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>{template?.name}</p>
                      <div style={{ fontSize: '14px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400, margin: 0 }}>
                        {template?.content}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              {alignment === 'Preview' && (
                <div style={{ padding: '10px 30px' }}>
                  <p style={{ fontSize: '16px', color: '#424242', fontFamily: 'Poppins', fontWeight: 400 }}>Message Preview</p>
                  <Box
                    sx={{
                      padding: '10px',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      backgroundColor: '#f2f5fa',
                      width: '100%',
                      height: '455px',
                      overflowY: 'scroll',
                      '&::-webkit-scrollbar': {
                        width: '5px',
                        height: '5px'
                      },
                      '&::-webkit-scrollbar-thumb': {
                        backgroundColor: '#d9d9d9',
                        borderRadius: '4px'
                      },
                      '&::-webkit-scrollbar-thumb:hover': {
                        backgroundColor: '#cccccc'
                      },
                      '&::-webkit-scrollbar-track': {
                        backgroundColor: '#f0f0f0'
                      }
                    }}
                  >
                    <ReactMarkdown>{templateContent}</ReactMarkdown>
                  </Box>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default EditSequenceTemplateDialog;
