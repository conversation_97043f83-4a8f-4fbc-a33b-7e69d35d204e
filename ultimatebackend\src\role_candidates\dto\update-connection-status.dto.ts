import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class UpdateConnectionStatusDto {
  @ApiProperty({
    description: 'The LinkedIn connection send status',
    enum: ['SENT', 'NOT_SENT', 'READY_TO_SEND', 'RECEIVED', 'NOT_RECEIVED'],
    example: 'SENT',
  })
  @IsEnum(['SENT', 'NOT_SENT', 'READY_TO_SEND', 'RECEIVED', 'NOT_RECEIVED'])
  connectionStatus: 'SENT' | 'NOT_SENT' | 'READY_TO_SEND' | 'RECEIVED' | 'NOT_RECEIVED';

  @ApiPropertyOptional({
    description: 'Optional response status for the connection request',
    example: 'Accepted',
  })
  @IsOptional()
  @IsString()
  responseStatus?: string;
}
