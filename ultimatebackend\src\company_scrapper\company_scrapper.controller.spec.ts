import { Test, TestingModule } from '@nestjs/testing';
import { CompanyScrapperController } from './company_scrapper.controller';
import { CompanyScrapperService } from './company_scrapper.service';

describe('CompanyScrapperController', () => {
  let controller: CompanyScrapperController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CompanyScrapperController],
      providers: [CompanyScrapperService],
    }).compile();

    controller = module.get<CompanyScrapperController>(CompanyScrapperController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
