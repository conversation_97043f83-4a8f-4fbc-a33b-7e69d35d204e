import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('company_scrapper')
export class CompanyScrapper {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  scrapper_profile_name: string;

  @Column({ nullable: true })
  companies_extracted: number;

  @Column({ type: 'date', nullable: true })
  date: string;

  @CreateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  created_at: Date;

  @UpdateDateColumn({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  updated_at: Date;
}
