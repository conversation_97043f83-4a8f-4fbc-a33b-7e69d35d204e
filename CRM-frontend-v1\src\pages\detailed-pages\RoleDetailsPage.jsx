import { Icon } from '@iconify/react';
import { <PERSON><PERSON>, Row, Col, Typo<PERSON>, Button, message } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';
import { Outlet, useNavigate, useParams } from 'react-router';
import RoleDetailStatusOptionsPopover from './components/RoleDetailStatusOptionsPopover';
import DynamicTabs from './components/RoleDetailTabs';
import { useSelector } from 'react-redux';
import { Get, Post } from 'actions/API/apiActions';
import { API_URLS } from 'constants/apiUrls';
import { Helmet } from 'react-helmet-async';
import { calculateDuration } from 'components/helpers/timeFormatHelper';
import UpdateRole from 'components/forms/UpdateRole';

const { Title, Text } = Typography;

function RoleDetailsPage() {
  const { roleService, roleId } = useParams();
  const [role, setRole] = useState(null);
  const [roleDetails, setRoleDetails] = useState();
  const decodedRoleService = decodeURIComponent(roleService);
  const userDesignation = useSelector((state) => state.user.userDesignation);
  const userId = useSelector((state) => state.user.userId);
  const [timer, setTimer] = useState(null);
  const [openEditRoleDialog, setOpenEditRoleDialog] = useState(false);
  const [roleCandidateCount, setRoleCandidateCount] = useState(false);

  // console.log("Dsadsad", userDesignation);
  // console.log('userRolehsdfdsf', userRole);
  const getRoleById = () => {
    try {
      const url = API_URLS.getRoleById.replace(':id', roleId);
      Get(
        {},
        url,
        (response) => {
          setRole(response);
        },
        (error) => {
          // console.log(error);
          message.error(error?.response?.message || 'Failed to get all services. Try refreshing the page!');
        }
      );
    } catch (error) {
      message.error('Failed to get service stats. Try refreshing the page!');
    }
  };
  const getCandidateCount = useCallback(() => {
    try {
      Get(
        {},
        API_URLS.getRoleCandidateCount.replace(':roleId', roleId),
        (resp) => {
          console.log('Candidate count response:', resp);
          setRoleCandidateCount(resp);
        },
        (error) => {
          // message.error(error?.response?.message || 'Failed to fetch candidate count. Try refreshing the page!');
        }
      );
    } catch (error) {
      // message.error('Failed to fetch candidate count. Try refreshing the page!');
    }
  }, [roleId]);

  useEffect(() => {
    getCandidateCount();
  }, []);
  // Dynamic tab items based on role services
  const dynamicTabs = [
    {
      key: '1',
      label: 'Details',
      path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/role-details`
    },
    {
      key: '2',
      label: 'All Candidates',
      path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/all-candidates`
    }
    // Applications tab will be added conditionally below
  ];

  // Add Applications tab based on role service
  if (decodedRoleService === 'Pre Qualification') {
    dynamicTabs.push(
      {
        key: '3',
        label: `Applications ( ${roleCandidateCount?.total_count || 0} )`,
        path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/applications`
      },
      {
        key: '4',
        label: 'Submissions',
        path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/submission_stage`
      }
    );
  } else if (decodedRoleService?.includes('360')) {
    dynamicTabs.push(
      {
        key: '3',
        label: `Applications ( ${roleCandidateCount?.total_count || 0} )`,
        path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/applications`
      },
      {
        key: '4',
        label: `Submissions ( ${roleCandidateCount?.total_count || 0} )`,
        path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/submission_stage`
      },
      {
        key: '5',
        label: `Placements ( 0/${roleCandidateCount?.total_count || 0} )`,
        path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/placements`
      }
    );
  } else {
    // Default Applications tab for other role services
    dynamicTabs.push({
      key: '3',
      label: `Applications ( ${roleCandidateCount?.total_count || 0} )`,
      path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/applications`
    });
  }
  if ((decodedRoleService?.includes('360') || decodedRoleService === 'Pre Qualification') && userDesignation === 'RECRUITER_CRM') {
    dynamicTabs.push({
      key: '6',
      label: 'Sequences',
      path: `/${encodeURIComponent(decodedRoleService)}/${roleId}/sequences`
    });
  }

  // console.log('jhsdfsdfdfsdf', role);

  const navigate = useNavigate();
  const [selectedStatus, setSelectedStatus] = useState('Open');
  // Extract active tab from URL
  const activeTabFromURL = dynamicTabs.find((tab) => location?.pathname?.includes(tab?.path))?.key || '1';

  useEffect(() => {
    // Update active tab whenever URL changes
    setActiveTab(activeTabFromURL);
  }, [location.pathname]);
  const [activeTab, setActiveTab] = useState(activeTabFromURL);

  useEffect(() => {
    getRoleById();
  }, [roleId]);

  useEffect(() => {
    if (role?.roleLogs?.length && userId) {
      const activeLog = role.roleLogs.find(
        (log) => log.action === 'STARTED' && log.status === 'IN_PROGRESS' && log.userId === userId && log.end_time === null
      );

      if (activeLog) {
        // Initial timer value
        setTimer(calculateDuration(activeLog?.start_time));

        // Update every second
        const interval = setInterval(() => {
          setTimer(calculateDuration(activeLog?.start_time));
        }, 1000);

        return () => clearInterval(interval); // Clear on unmount
      }
    }
  }, [role, userId]);

  const markRoleAsDone = () => {
    try {
      Post(
        {
          roleId: roleId,
          userId: userId
        },
        API_URLS.markRoleAsDone,
        (response) => {
          // console.log(response);
          message.success('Role marked as done successfully!');
        },
        (error) => {
          // console.log(error);
          message.error(error?.response?.message || 'Failed to mark role done. Try again!');
        }
      );
    } catch (error) {
      message.error('Failed to mark role done. Try again!');
    }
  };
  const leaveRole = () => {
    try {
      Post(
        {
          roleId: roleId,
          userId: userId
        },
        API_URLS.leaveRole,
        (response) => {
          // console.log(response);
          message.success('Role left successfully!');
        },
        (error) => {
          // console.log(error);
          message.error(error?.response?.message || 'Failed to leave role. Try again!');
        }
      );
    } catch (error) {
      message.error('Failed to leave role. Try again!');
    }
  };

  useEffect(() => {
    if (role) {
      setRoleDetails(role);
    }
    console.log('WHAT IS ROLE HERE:', role);
  }, [role, openEditRoleDialog]);
  return (
    <>
      <Helmet>
        <title>Role Details</title>
      </Helmet>
      <div
        style={{
          width: '100%',
          backgroundColor: 'white',
          padding: '15px 30px',
          paddingBottom: '0px',
          position: 'sticky',
          top: 0,
          zIndex: 1000,
          boxShadow: '0px 4px 8px rgba(0, 0, 0, 0.1)'
        }}
      >
        <Row align="middle" justify="space-between">
          <Row align="middle">
            {/* Avatar Icon */}
            <Avatar size={90} style={{ backgroundColor: '#AFCFFF', verticalAlign: 'middle', fontSize: '30px', fontWeight: '600' }}>
              {role?.title?.charAt(0).toUpperCase()}
            </Avatar>
            {/* Title & Status Section */}
            <Col style={{ marginLeft: '15px', flex: 1 }}>
              <Row align="middle">
                <Icon
                  icon="bx:arrow-back"
                  width="22"
                  height="22"
                  style={{ cursor: 'pointer' }}
                  onClick={() => navigate('/trial-roles/all-roles')}
                />
                <Title
                  level={4}
                  style={{
                    margin: '0 10px',
                    fontWeight: '600',
                    color: '#0C2340',
                    fontFamily: 'Poppins'
                  }}
                >
                  {[role?.title, role?.locations, role?.postal_code]
                    .filter(Boolean) // remove undefined/null/empty
                    .join(' - ')}
                </Title>
              </Row>
              <Row style={{ marginTop: '5px' }}>
                <RoleDetailStatusOptionsPopover
                  onSelect={(status) => {
                    setSelectedStatus(status?.props.children);
                  }}
                  status={selectedStatus}
                />
              </Row>
              {/* Details Row */}
              <Row style={{ marginTop: '5px' }}>
                <Text type="primary" style={{ margin: '0px 10px', fontSize: '13px' }}>
                  <strong>ID :</strong> {role?.id}
                </Text>
                <Text type="primary" style={{ margin: '0 10px' }}>
                  {role?.industry ? '|' : ''}
                </Text>
                <Text type="primary" style={{ fontSize: '13px', fontFamily: 'Poppins' }}>
                  {role?.industry ? role?.industry : ''}
                </Text>
                <Text type="primary" style={{ margin: '0 10px' }}>
                  |
                </Text>
                <Icon icon="proicons:location" width="16" height="16" />
                <Text type="primary" style={{ fontSize: '13px', fontFamily: 'Poppins' }}>
                  &nbsp;&nbsp;{role?.locations + ', ' + role?.postal_code}
                </Text>
              </Row>
            </Col>
          </Row>
          {role?.current_status?.includes('IN_PROGRESS') && (
            <div style={{ display: 'flex', gap: '10px' }}>
              <Button
                type="primary"
                icon={<Icon icon="pepicons-pencil:leave" width="16" height="16" />}
                style={{
                  borderRadius: '8px',
                  height: '35px',
                  width: '100%',
                  fontSize: '16px',
                  backgroundColor: '#32ade6',
                  fontWeight: 400
                }}
                onClick={() => {
                  leaveRole();
                }}
              >
                Leave Role
              </Button>
              <Button
                type="primary"
                icon={<Icon icon="hugeicons:tick-01" width="16" height="16" />}
                style={{
                  borderRadius: '8px',
                  height: '35px',
                  width: '100%',
                  fontSize: '16px',
                  backgroundColor: '#32ade6',
                  fontWeight: 400
                }}
                onClick={() => {
                  markRoleAsDone();
                }}
              >
                Mark Done
              </Button>
              <Button
                type="primary"
                icon={<Icon icon="mdi:timer-sand-complete" width="16" height="16" />}
                style={{
                  borderRadius: '8px',
                  height: '35px',
                  width: '100%',
                  fontSize: '16px',
                  backgroundColor: '#32ade6',
                  fontWeight: 400
                }}
                onClick={() => {}}
              >
                In Progress
              </Button>
              <Button
                type="primary"
                style={{
                  borderRadius: '8px',
                  height: '35px',
                  width: '100%',
                  fontSize: '16px',
                  backgroundColor: '#ff9500',
                  fontWeight: 400
                }}
                onClick={() => {}}
              >
                {timer || '00 : 00 : 00'}
              </Button>
            </div>
          )}
          {(userDesignation === 'ACCOUNT_EXECUTIVE' || userDesignation === 'BUSINESS_DEVELOPMENT_EXECUTIVE') && (
            <div style={{ display: 'flex', gap: '10px' }}>
              <Button
                type="primary"
                icon={<Icon icon="ic:twotone-edit" width="16" height="16" />}
                style={{
                  borderRadius: '8px',
                  height: '35px',
                  width: '100%',
                  fontSize: '16px',
                  backgroundColor: '#32ade6',
                  fontWeight: 400
                }}
                onClick={() => {
                  setOpenEditRoleDialog(true);
                }}
              >
                Edit Role
              </Button>
            </div>
          )}
        </Row>
        <Row style={{ marginTop: '10px', marginBottom: 0 }}>
          <DynamicTabs
            items={dynamicTabs?.map(({ key, label }) => ({ key, label }))}
            onChangeTab={(key) => {
              setActiveTab(key);
              navigate(dynamicTabs.find((tab) => tab.key === key)?.path);
            }}
            activeKey={activeTab}
          />
        </Row>
        {openEditRoleDialog && (
          <UpdateRole
            open={openEditRoleDialog}
            onClose={() => {
              setOpenEditRoleDialog(false);
            }}
            roleDetails={roleDetails}
          />
        )}
      </div>
      {/* Render Tab Content Using Routing */}
      <div style={{ padding: '20px' }}>
        <Outlet />
      </div>
    </>
  );
}

export default RoleDetailsPage;
