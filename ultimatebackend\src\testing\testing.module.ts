import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SequenceLoadTestService } from './sequence-load-test.service';
import { SequenceLoadTestController } from './sequence-load-test.controller';
import { People } from '../people/people.entity';
import { PersonEmail } from '../email/email.entity';
import { PersonPhone } from '../phone/phone.entity';
import { RoleCandidate } from '../role-candidate/role-candidate.entity';
import { RoleSequence } from '../sequence/sequence.entity';
import { SequenceSteps } from '../sequence-steps/sequence_steps.entity';
import { CandidateSequenceStatus } from '../candidate-sequence-status/candidate-sequence-status.entity';
import { SequenceModule } from '../sequence/sequence.module';
import { CandidateSequenceStatusModule } from '../candidate-sequence-status/candidate-sequence-status.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      People,
      PersonEmail,
      PersonPhone,
      RoleCandidate,
      RoleSequence,
      SequenceSteps,
      CandidateSequenceStatus,
    ]),
    SequenceModule,
    CandidateSequenceStatusModule,
  ],
  providers: [SequenceLoadTestService],
  controllers: [SequenceLoadTestController],
  exports: [SequenceLoadTestService],
})
export class TestingModule {}
