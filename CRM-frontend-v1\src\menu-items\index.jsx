import calendar from './bd/calendar';
import dashboard from './bd/dashboard';
import invoices from './bd/invoiced';
import mailbox from './bd/mailbox';
import opportunities from './bd/opportunities';
import templates from './bd/templates';
import trialClients from './bd/trial-clients';
import trialRoles from './bd/trial-roles';
import ACMDashboard from './acm/ACMDashboard';
import ACMRoles from './acm/ACMRoles';
import ACMClients from './acm/ACMClients';
import ACMInvoices from './acm/ACMInvoices';
import ACMMailbox from './acm/ACMMailbox';
import ACMCalendar from './acm/ACMCalendar';
import entries from './acm/entries';
import resourcer_dashboard from './resourcer/resourcer-dashboard';
import resourcer_roles from './resourcer/resourcer-roles';
// import resourcer_calendar from './resourcer/resourcer-calendar';
import recruiter_dashboard from './recruiter/recruiter-dashboard';
import recruiter_roles from './recruiter/recruiter-roles';
import recruiter_mailbox from './recruiter/recruiter-mailbox';
import recruiter_calendar from './recruiter/recruiter-calendar';
import recruiter_templates from './recruiter/recruiter-templates';
import { store } from 'redux/store';
import AdminDashboard from './admin/AdminDashboard';
import users from './admin/users';
import departments from './admin/Departments';
import ThirdParty from './admin/ThirdParty';
import WebTraffic from './admin/WebTraffic';
import Settings from './admin/Settings';
import fileManager from './common/file-manager';
import sequenceDashboard from './common/sequence-dashboard';
import Client_dashboard from './clientSide/client-dashboard';
import client_jobs from './clientSide/jobs';
import candidates from './clientSide/candidates';
import automations from './clientSide/automations';
import subscriptions from './clientSide/subscriptions';

// ==============================|| MENU ITEMS ||============================== //
const getMenuItems = () => {
  const userRole = store.getState().user.userRole; // ✅ Get Redux State here dynamically
  const userDesignation = store.getState().user.userDesignation; // ✅ Get Redux State here dynamically
  // console.log("userRole:", userRole);

  const menuItems = {
    items: []
  };

  // Conditional Menu Based on User Role
  if (userDesignation === 'BUSINESS_DEVELOPMENT_EXECUTIVE') {
    menuItems.items = [dashboard, trialRoles, trialClients, opportunities, invoices, mailbox, templates, calendar, sequenceDashboard, fileManager];
  } else if (userDesignation === 'ACCOUNT_EXECUTIVE') {
    menuItems.items = [ACMDashboard, ACMClients, ACMRoles, ACMInvoices, ACMMailbox, ACMCalendar, entries, sequenceDashboard, fileManager];
  } else if (userDesignation === 'RESOURCER_CRM') {
    menuItems.items = [resourcer_dashboard, resourcer_roles, sequenceDashboard, fileManager];
  } else if (userDesignation === 'RECRUITER_CRM') {
    menuItems.items = [recruiter_dashboard, recruiter_roles, recruiter_mailbox, recruiter_calendar, recruiter_templates, sequenceDashboard, fileManager];
  } else if (userRole === 'SUPER_ADMIN') {
    menuItems.items = [AdminDashboard, users, departments, ThirdParty, WebTraffic, Settings, sequenceDashboard, fileManager];
  } else if (userRole === 'CLIENT') {
    menuItems.items = [Client_dashboard, client_jobs, candidates, automations, subscriptions]
  }

  else {
    menuItems.items = [sequenceDashboard, fileManager];
  }

  return menuItems;
};

export default getMenuItems;
