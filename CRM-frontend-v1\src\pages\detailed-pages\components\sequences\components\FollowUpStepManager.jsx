import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Button,
  Space,
  Card,
  List,
  Tag,
  message,
  Divider,
  Tooltip,
  Popconfirm,
  Alert
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  SendOutlined,
  EditOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import * as sequenceAPI from '../../../../../api/sequenceExecution';

const { Option } = Select;
const { TextArea } = Input;

const FollowUpStepManager = ({ 
  visible, 
  onClose, 
  stepId, 
  stepName, 
  onFollowUpCreated 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [existingFollowUps, setExistingFollowUps] = useState([]);
  const [followUpSteps, setFollowUpSteps] = useState([]);

  useEffect(() => {
    if (visible && stepId) {
      loadExistingFollowUps();
      // Initialize with one empty follow-up step
      setFollowUpSteps([{
        id: Date.now(),
        name: '',
        medium: 'EMAIL',
        type: 'NO_RESPONSE_FOLLOW_UP',
        order: 1,
        noResponseDelayHours: 24,
        templateId: 1
      }]);
    }
  }, [visible, stepId]);

  const loadExistingFollowUps = async () => {
    try {
      const response = await sequenceAPI.getFollowUpSteps(stepId);
      setExistingFollowUps(response.followUpSteps || []);
    } catch (error) {
      console.error('Error loading existing follow-ups:', error);
    }
  };

  const addFollowUpStep = () => {
    const newStep = {
      id: Date.now(),
      name: '',
      medium: 'EMAIL',
      type: 'NO_RESPONSE_FOLLOW_UP',
      order: followUpSteps.length + 1,
      noResponseDelayHours: 24,
      templateId: 1
    };
    setFollowUpSteps([...followUpSteps, newStep]);
  };

  const removeFollowUpStep = (stepId) => {
    setFollowUpSteps(followUpSteps.filter(step => step.id !== stepId));
  };

  const updateFollowUpStep = (stepId, field, value) => {
    setFollowUpSteps(followUpSteps.map(step => 
      step.id === stepId ? { ...step, [field]: value } : step
    ));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      // Validate that all steps have required fields
      const invalidSteps = followUpSteps.filter(step => 
        !step.name || !step.medium || !step.noResponseDelayHours
      );
      
      if (invalidSteps.length > 0) {
        message.error('Please fill in all required fields for follow-up steps');
        return;
      }

      // Prepare data for API
      const followUpStepsData = followUpSteps.map((step, index) => ({
        name: step.name,
        type: 'NO_RESPONSE_FOLLOW_UP',
        medium: step.medium,
        templateId: step.templateId || 1,
        order: index + 1,
        noResponseDelayHours: step.noResponseDelayHours
      }));

      await sequenceAPI.createFollowUpSteps(stepId, followUpStepsData);
      
      message.success(`Created ${followUpStepsData.length} follow-up steps successfully`);
      
      if (onFollowUpCreated) {
        onFollowUpCreated();
      }
      
      onClose();
      
    } catch (error) {
      console.error('Error creating follow-up steps:', error);
      message.error('Failed to create follow-up steps');
    } finally {
      setLoading(false);
    }
  };

  const getMediumIcon = (medium) => {
    const icons = {
      EMAIL: '📧',
      SMS: '📱',
      WHATSAPP: '💬',
      LINKEDIN: '💼',
      CALL: '📞'
    };
    return icons[medium] || '📧';
  };

  const getMediumColor = (medium) => {
    const colors = {
      EMAIL: 'blue',
      SMS: 'green',
      WHATSAPP: 'cyan',
      LINKEDIN: 'purple',
      CALL: 'orange'
    };
    return colors[medium] || 'blue';
  };

  return (
    <Modal
      title={
        <Space>
          <ClockCircleOutlined />
          Follow-up Steps for: {stepName}
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>
          Cancel
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          loading={loading}
          onClick={handleSubmit}
          disabled={followUpSteps.length === 0}
        >
          Create Follow-up Steps
        </Button>
      ]}
    >
      <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
        {/* Existing Follow-ups */}
        {existingFollowUps.length > 0 && (
          <>
            <Alert
              message="Existing Follow-up Steps"
              description={`This step already has ${existingFollowUps.length} follow-up step(s) configured.`}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <List
              size="small"
              dataSource={existingFollowUps}
              renderItem={(item, index) => (
                <List.Item>
                  <Space>
                    <Tag color={getMediumColor(item.medium)}>
                      {getMediumIcon(item.medium)} {item.medium}
                    </Tag>
                    <span>{item.name}</span>
                    <Tag color="orange">
                      {item.noResponseDelayHours}h delay
                    </Tag>
                  </Space>
                </List.Item>
              )}
              style={{ marginBottom: 16 }}
            />
            <Divider />
          </>
        )}

        {/* New Follow-up Steps */}
        <div style={{ marginBottom: 16 }}>
          <Space align="center">
            <InfoCircleOutlined style={{ color: '#1890ff' }} />
            <span style={{ fontWeight: 500 }}>Configure No-Response Follow-up Steps</span>
          </Space>
          <p style={{ color: '#666', margin: '8px 0', fontSize: '14px' }}>
            These steps will be automatically triggered if candidates don't respond within the specified time.
          </p>
        </div>

        {followUpSteps.map((step, index) => (
          <Card
            key={step.id}
            size="small"
            title={`Follow-up Step ${index + 1}`}
            extra={
              followUpSteps.length > 1 && (
                <Popconfirm
                  title="Remove this follow-up step?"
                  onConfirm={() => removeFollowUpStep(step.id)}
                >
                  <Button 
                    type="text" 
                    danger 
                    icon={<DeleteOutlined />}
                    size="small"
                  />
                </Popconfirm>
              )
            }
            style={{ marginBottom: 16 }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', gap: 16 }}>
                <div style={{ flex: 1 }}>
                  <label style={{ display: 'block', marginBottom: 4, fontWeight: 500 }}>
                    Step Name *
                  </label>
                  <Input
                    placeholder="e.g., Follow-up Email"
                    value={step.name}
                    onChange={(e) => updateFollowUpStep(step.id, 'name', e.target.value)}
                  />
                </div>
                <div style={{ width: 150 }}>
                  <label style={{ display: 'block', marginBottom: 4, fontWeight: 500 }}>
                    Medium *
                  </label>
                  <Select
                    value={step.medium}
                    onChange={(value) => updateFollowUpStep(step.id, 'medium', value)}
                    style={{ width: '100%' }}
                  >
                    <Option value="EMAIL">📧 Email</Option>
                    <Option value="SMS">📱 SMS</Option>
                    <Option value="WHATSAPP">💬 WhatsApp</Option>
                    <Option value="LINKEDIN">💼 LinkedIn</Option>
                    <Option value="CALL">📞 Call</Option>
                  </Select>
                </div>
              </div>

              <div style={{ display: 'flex', gap: 16 }}>
                <div style={{ width: 200 }}>
                  <label style={{ display: 'block', marginBottom: 4, fontWeight: 500 }}>
                    Delay (Hours) *
                  </label>
                  <InputNumber
                    min={1}
                    max={168} // 1 week
                    value={step.noResponseDelayHours}
                    onChange={(value) => updateFollowUpStep(step.id, 'noResponseDelayHours', value)}
                    style={{ width: '100%' }}
                    placeholder="24"
                  />
                </div>
                <div style={{ width: 150 }}>
                  <label style={{ display: 'block', marginBottom: 4, fontWeight: 500 }}>
                    Template ID
                  </label>
                  <InputNumber
                    min={1}
                    value={step.templateId}
                    onChange={(value) => updateFollowUpStep(step.id, 'templateId', value)}
                    style={{ width: '100%' }}
                    placeholder="1"
                  />
                </div>
              </div>
            </Space>
          </Card>
        ))}

        <Button
          type="dashed"
          onClick={addFollowUpStep}
          icon={<PlusOutlined />}
          style={{ width: '100%', marginTop: 8 }}
        >
          Add Another Follow-up Step
        </Button>

        <Alert
          message="How it works"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li>Follow-up steps are triggered automatically when candidates don't respond</li>
              <li>The system checks every hour for steps that need follow-up</li>
              <li>Each follow-up step can have a different delay and communication medium</li>
              <li>Follow-ups stop if the candidate responds to any step in the sequence</li>
            </ul>
          }
          type="info"
          showIcon
          style={{ marginTop: 16 }}
        />
      </div>
    </Modal>
  );
};

export default FollowUpStepManager;
