import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { CreatePeopleAssignmentDto } from './dto/create-people-assignment.dto';
import { UpdatePeopleAssignmentDto } from './dto/update-people-assignment.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { People } from 'src/people/people.entity';
import { DataSource, In, IsNull, Not, QueryBuilder, Repository } from 'typeorm';
import { Company } from 'src/company/company.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import {
  AssignmentStatus,
  AssignmentType,
  PeopleAssignment,
} from './entities/people-assignment.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { Users } from 'src/users/users.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import {
  AddEmailFromSpreadsheetDto,
  AddReplacementEmailsDto,
  MarkAsEmailNotFoundDto,
} from './dto/add-email-from-spreadsheet.dto';
import e from 'express';
import { isBusinessEmail, validateEmail } from 'src/helper/EmailValidator';
import { AssignPersonsToUserDto } from './dto/get-assigned-persons.dto';
import { PersonType } from 'src/people/dto/people.enums';

export interface GetAssignedPersonsQuery {
  userId: string;
  havingNoContactInfo?: boolean;
  needReplacement?: boolean;
  noEmailFound?: boolean;
  editEmailInfo?: boolean;
  findHiringPersons?: boolean;
  findBounceBacks?: boolean;
  searchString?: string;
  page?: number;
  pageSize?: number;
}

@Injectable()
export class PeopleAssignmentsService {
  constructor(
    @InjectRepository(People)
    private peopleRepository: Repository<People>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(PeopleAssignment)
    private peopleAssignmentRepository: Repository<PeopleAssignment>,
    @InjectRepository(PersonPhone)
    private personPhoneRepository: Repository<PersonPhone>,
    @InjectRepository(Users)
    private usersRepository: Repository<Users>,
    @InjectRepository(Jobs)
    private jobsRepository: Repository<Jobs>,
    @InjectRepository(PersonEmail)
    private personEmailRepository: Repository<PersonEmail>,
    private dataSource: DataSource,
  ) {}

  create(createPeopleAssignmentDto: CreatePeopleAssignmentDto) {
    return 'This action adds a new peopleAssignment';
  }

  findAll() {
    return `This action returns all peopleAssignments`;
  }

  findOne(id: number) {
    return `This action returns a #${id} peopleAssignment`;
  }

  update(id: number, updatePeopleAssignmentDto: UpdatePeopleAssignmentDto) {
    return `This action updates a #${id} peopleAssignment`;
  }

  remove(id: number) {
    return `This action removes a #${id} peopleAssignment`;
  }

  async getAssignedPersonsByUserId(query: GetAssignedPersonsQuery) {
    const {
      userId,
      havingNoContactInfo,
      needReplacement,
      noEmailFound,
      editEmailInfo,
      findHiringPersons,
      findBounceBacks,
      searchString,
      page = 0,
      pageSize = 10,
    } = query;

    const limit = pageSize;
    const skip = page * limit;

    const findPersonsByUserId = await this.peopleAssignmentRepository.find({
      where: {
        leadUserId: userId,
        is_working_completed: false,
        is_business_email_added: false,
        is_found: false,
      },
      order: {
        created_at: 'DESC',
        id: 'ASC',
      },
      take: limit,
      skip: skip,
      relations: [
        'person',
        'person.company',
        'person.emails',
        'person.phones',
        'leadUser',
        'company',
      ],
    });

    const filteredAssignments = this.applyFilters(findPersonsByUserId, {
      havingNoContactInfo,
      needReplacement,
      noEmailFound,
      editEmailInfo,
      findHiringPersons,
      findBounceBacks,
      searchString,
    });

    return this.formatAndPaginateResults(filteredAssignments, page, pageSize);
  }

  private applyFilters(assignments: PeopleAssignment[], filters: any) {
    const {
      havingNoContactInfo,
      needReplacement,
      noEmailFound,
      editEmailInfo,
      findHiringPersons,
      findBounceBacks,
      searchString,
    } = filters;

    let filtered = assignments;

    if (havingNoContactInfo === 'true') {
      filtered = filtered.filter((a) => !a.is_business_email_added);
    }
    if (needReplacement === 'true') {
      filtered = filtered.filter((a) => a.is_replacement_needed);
    }
    if (noEmailFound === 'true') {
      filtered = filtered.filter((a) => a.is_email_not_found);
    }
    if (editEmailInfo === 'true') {
      filtered = filtered.filter((a) => a.is_email_info_added);
    }
    if (findHiringPersons === 'true') {
      filtered = filtered.filter((a) => a.is_hiring_person);
    }
    if (findBounceBacks === 'true') {
      filtered = filtered.filter((a) => a.is_bounce_back);
    }

    if (searchString) {
      filtered = filtered.filter(
        (a) =>
          a.person?.first_name
            ?.toLowerCase()
            .includes(searchString.toLowerCase()) ||
          a.person?.last_name
            ?.toLowerCase()
            .includes(searchString.toLowerCase()) ||
          a.person?.current_title
            ?.toLowerCase()
            .includes(searchString.toLowerCase()) ||
          a.person?.company?.name
            ?.toLowerCase()
            .includes(searchString.toLowerCase()) ||
          a.company?.name?.toLowerCase().includes(searchString.toLowerCase()),
      );
    }

    return filtered;
  }

  private async formatAndPaginateResults(
    assignments: PeopleAssignment[],
    page: number,
    pageSize: number,
    message?: string,
  ) {
    const startIndex = page * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedAssignments = assignments.slice(startIndex, endIndex);

    const formattedResults = await Promise.all(
      paginatedAssignments.map(async (assignment) => {
        const person = assignment.person;

        // Get business and personal emails
        const businessEmails =
          person?.emails?.filter((email) => email.email_type === 'BUSINESS') ||
          [];
        const personalEmails =
          person?.emails?.filter((email) => email.email_type === 'PERSONAL') ||
          [];

        // Get business and personal phones
        const businessPhones =
          person?.phones?.filter((phone) => phone.phone_type === 'BUSINESS') ||
          [];
        const personalPhones =
          person?.phones?.filter((phone) => phone.phone_type === 'PERSONAL') ||
          [];

        // Get claimed_by info
        const claimed_by = assignment.leadUser
          ? {
              id: assignment.leadUser.id,
              full_name: assignment.leadUser.full_name,
              email: assignment.leadUser.email,
            }
          : null;

        const replacement_claim = assignment.is_replacement
          ? {
              is_replacement: assignment.is_replacement,
              replacement_date: assignment.updated_at,
            }
          : null;

        // Get latest job post
        const latestJobPost = person?.jobs?.[0] || null;

        return {
          id: person?.id || assignment.id,
          profile_url: person?.profile_url,
          full_name: person
            ? `${person.first_name || ''} ${person.last_name || ''}`.trim()
            : '',
          company: person?.company || assignment.company,
          current_title: person?.current_title,
          avator: person?.profile_img,
          is_business_email_added: assignment.is_business_email_added,
          businessEmails: this.formatEmailData(businessEmails, assignment),
          personalEmails: this.formatEmailData(personalEmails, assignment),
          businessPhones: this.formatPhoneData(businessPhones),
          personalPhones: this.formatPhoneData(personalPhones),
          claimed_by,
          replacement_claim,
          latestJobPost,
        };
      }),
    );

    return {
      message: message ? message : 'People fetched successfully',
      data: formattedResults,
      total: assignments.length,
      page,
      pageSize,
      totalPages: Math.ceil(assignments.length / pageSize),
    };
  }

  private formatEmailData(emails: PersonEmail[], assignment: PeopleAssignment) {
    return emails.map((e) => ({
      id: e.id,
      email_id: e.email,
      user_id: e.isReplacedBy,
      status: assignment.status,
      is_verified_by_amazon: assignment.is_verified_by_amazon,
      is_default_email: e.is_default,
      type: assignment.type,
    }));
  }

  private formatPhoneData(phones: any[]) {
    return phones.map((phone) => ({
      id: phone.id,
      phone_number: phone.phone_number,
      phone_type: phone.phone_type,
      is_default: phone.is_default || false,
    }));
  }

  async assignPersonsByUserId(queryParams: AssignPersonsToUserDto) {
    const { userId, page, pageSize } = queryParams;
    try {
      const pageInt = parseInt(page) || 0;
      const pageSizeInt = parseInt(pageSize) || 10;

      const alreadyAssignedToUser = await this.peopleAssignmentRepository.find({
        where: {
          leadUserId: userId,
          is_working_completed: false,
          is_business_email_added: false,
          is_found: false,
        },
        relations: [
          'person',
          'person.company',
          'person.emails',
          'person.phones',
          'leadUser',
          'company',
        ],
      });

      if (alreadyAssignedToUser.length > 0) {
        const message = `Please complete work on your already assigned ${alreadyAssignedToUser.length} persons.`;
        return this.formatAndPaginateResults(
          alreadyAssignedToUser,
          pageInt,
          pageSizeInt,
          message,
        );
      }

      // Step 1: Fetch 50 people from person_assignment with specific conditions
      const step1Assignments = await this.peopleAssignmentRepository.find({
        where: {
          leadUserId: IsNull(),
          is_working_completed: false,
          is_business_email_added: false,
          is_found: false,
          is_email_not_found: false,
        },
        take: 50,
        relations: [
          'person',
          'person.company',
          'person.emails',
          'person.phones',
          'leadUser',
          'company',
        ],
      });

      // Step 2: Fetch 25 people from person_assignment with replacement conditions
      const step2Assignments = await this.peopleAssignmentRepository.find({
        where: {
          leadUserId: IsNull(),
          is_replacement_needed: true,
          is_email_not_found: true,
        },
        take: 25,
        relations: [
          'person',
          'person.company',
          'person.emails',
          'person.phones',
          'leadUser',
          'company',
        ],
      });

      // Step 3: Fetch 25 people from person_assignment with bounce back conditions
      const step3Assignments = await this.peopleAssignmentRepository.find({
        where: {
          leadUserId: null,
          is_bounce_back: true,
          is_business_email_added: true,
          is_found: true,
        },
        take: 25,
        relations: [
          'person',
          'person.company',
          'person.emails',
          'person.phones',
          'leadUser',
          'company',
        ],
      });

      // Merge all results
      let allAssignments = [
        ...step1Assignments,
        ...step2Assignments,
        ...step3Assignments,
      ];

      // Check if we have 100 people
      if (allAssignments.length === 100) {
        return this.formatAndPaginateResults(
          allAssignments,
          pageInt,
          pageSizeInt,
        );
      }

      // If length doesn't match 100, fetch 100 people with step1 conditions
      const fallbackAssignments = await this.peopleAssignmentRepository.find({
        where: {
          leadUserId: userId,
          is_working_completed: false,
          is_business_email_added: false,
          is_found: false,
        },
        take: 100,
        relations: [
          'person',
          'person.company',
          'person.emails',
          'person.phones',
          'leadUser',
          'company',
        ],
      });

      // Check if we have 100 people
      if (fallbackAssignments.length === 100) {
        return this.formatAndPaginateResults(
          fallbackAssignments,
          pageInt,
          pageSizeInt,
        );
      }

      // If still not 100, fetch people from People entity and create new assignments
      // First, get existing person IDs from people_assignment
      const existingPersonIds = await this.peopleAssignmentRepository.find({
        select: ['personId'],
        where: {
          personId: Not(IsNull()),
        },
      });

      const existingPersonIdsArray = existingPersonIds
        .map((item) => item.personId)
        .filter((id) => id !== null);

      // Fetch 100 people whose IDs are not in people_assignment
      const whereCondition =
        existingPersonIdsArray.length > 0
          ? { id: Not(In(existingPersonIdsArray)) }
          : {};

      const availablePeople = await this.peopleRepository.find({
        where: {
          id: Not(In(existingPersonIdsArray)),
          person_type: PersonType.JOB_POST_LEAD,
          // is_hiring: true,
        },
        take: 100,
        relations: ['company', 'emails', 'phones', 'sector', 'country'],
      });

      // Create new assignments without emails
      const newAssignments = [];
      for (const person of availablePeople) {
        const assignment = this.peopleAssignmentRepository.create({
          personId: person.id,
          email: null,
          leadUserId: userId,
          assignment_date: new Date(),
          person: person,
          companyId: person.companyId,
          countryId: person.countryId,
          company: person.company,
          sectorId: person.sectorId,
          sector: person.sector,
        });
        newAssignments.push(assignment);
      }

      if (newAssignments.length > 0) {
        await this.peopleAssignmentRepository.save(newAssignments);
      }

      return this.formatAndPaginateResults(
        newAssignments,
        pageInt,
        pageSizeInt,
      );
    } catch (error) {
      console.log('Error in getAssignments:', error);
      throw new InternalServerErrorException('Error fetching assignments');
    }
  }

  async addEmailsFromSpreadSheet(data: AddEmailFromSpreadsheetDto) {
    const { userId, businessEmails } = data;

    if (businessEmails.length === 0) {
      return {
        message: 'No emails found in the spreadsheet',
        data: [],
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    // First validate all emails
    const validationResults = await Promise.all(
      businessEmails.map(async (emailObj) => {
        const result = await validateEmail(emailObj.email);
        let isBusinessDomain = isBusinessEmail(
          emailObj.email,
          emailObj.websiteLink,
        );

        if (!isBusinessDomain && emailObj.secondaryLink) {
          isBusinessDomain = isBusinessEmail(
            emailObj.email,
            emailObj.secondaryLink,
          );
        }

        const isValid =
          result.validators.regex.valid &&
          result.validators.disposable.valid &&
          isBusinessDomain;

        return {
          emailObj,
          isValid: isValid,
        };
      }),
    );

    const validEmails = validationResults
      .filter((result) => result.isValid)
      .map((result) => result.emailObj);

    const invalidEmails = validationResults
      .filter((result) => !result.isValid)
      .map((result) => result.emailObj);

    const uniqueEmailIds = [
      ...new Set(validEmails.map((email) => email.email)),
    ];

    if (validEmails.length === 0) {
      return {
        message: `All ${businessEmails.length} emails are invalid or not business emails!`,
        data: [],
        invalidEmails: invalidEmails,
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    const existingEmails = await this.personEmailRepository.find({
      where: {
        email: In(uniqueEmailIds),
      },
      relations: ['person'],
    });

    const newEmails = validEmails.filter(
      (email) =>
        !existingEmails.some((existing) => existing.email === email.email),
    );

    if (newEmails.length === 0) {
      return {
        message: `All ${validEmails.length} valid emails already exist in system!`,
        data: [],
        invalidEmails: invalidEmails,
        existingEmails: existingEmails.length,
        total: 0,
        page: 0,
        pageSize: 0,
        totalPages: 0,
      };
    }

    const emailsToAdd = newEmails.map((e) => ({
      email: e.email,
      is_default: e.is_default,
      personId: e.person_id,
      email_type: 'BUSINESS',
      is_replaced: false,
      isReplacedBy: null,
      isAddedBy: userId,
    }));

    return await this.dataSource.transaction(async (manager) => {
      try {
        const savedEmails = await manager
          .getRepository(PersonEmail)
          .insert(emailsToAdd);

        const uniquePersonIds = [
          ...new Set(newEmails.map((email) => email.person_id)),
        ];

        await manager.getRepository(PeopleAssignment).update(
          { personId: In(uniquePersonIds) },
          {
            is_business_email_added: true,
            is_found: true,
            is_verified: true,
            is_default_email: true,
            is_email_info_added: true,
            is_verified_by_lead_expert: true,
            is_working_completed: true,
            leadUserId: userId,
          },
        );

        const insertedIds = savedEmails.identifiers.map((i) => i.id);
        await Promise.all(
          insertedIds.map((emailId, idx) => {
            const personId = newEmails[idx].person_id;
            return manager
              .getRepository(People)
              .update({ id: personId }, { emailId });
          }),
        );

        return {
          message: `${newEmails.length} emails added successfully! ${existingEmails.length} emails already exist and ${invalidEmails.length} emails were invalid.`,
          data: [],
          addedEmails: newEmails.length,
          existingEmails: existingEmails.length,
          invalidEmails: invalidEmails.length,
          total: newEmails.length,
          page: 0,
          pageSize: 0,
          totalPages: 0,
        };
      } catch (error) {
        // Transaction will automatically rollback on error
        console.log('Failed to add emails:', error);
        throw new Error(`Failed to add emails: ${error.message}`);
      }
    });
  }

  async addReplacementEmails(data: AddReplacementEmailsDto) {}

  async markAsEmailNotFound(data: MarkAsEmailNotFoundDto) {
    const { userId, persons } = data;
    try {
      await this.peopleAssignmentRepository.update(
        { personId: In(persons) },
        {
          is_found: false,
          is_email_not_found: true,
          is_replacement_needed: true,
          is_working_completed: false,
          leadUserId: null,
          not_found_by: userId,
        },
      );
      return { message: 'Emails marked as not found successfully!' };
    } catch (error) {
      console.log('Failed to mark emails as not found:', error);
      throw new Error(`Failed to mark emails as not found: ${error.message}`);
    }
  }
}
