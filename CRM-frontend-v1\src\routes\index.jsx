import { createBrowserRouter, Navigate } from 'react-router-dom';
import LoginRoutes from './LoginRoutes';
import BDRoutes from './BDRoutes';
import RoleDetailRoutes from './RoleDeatilRoutes';
import ACMRoutes from './ACMRoutes';
import ResourcerRoutes from './ResourcerRoutes';
import RecruiterRoutes from './RecruiterRoutes';
import CommonRoutes from './CommonRoutes';
import ClientDetailsRoutes from './ClientDetailsRoutes';
import AdminRoutes from './AdminRoutes';
import DashboardMinimalRoutes from './DashboardMinimalRoutes';
import ClientRoutes from './ClientRoutes';
// ==============================|| ROUTING RENDER ||============================== //

const router = createBrowserRouter(
  [
    {
      path: '/',
      element: <Navigate to="/login" replace />,
    },
    LoginRoutes,
    BDRoutes,
    RoleDetailRoutes,
    CommonRoutes,
    ACMRoutes,
    ResourcerRoutes,
    RecruiterRoutes,
    ClientDetailsRoutes,
    AdminRoutes,
    ClientRoutes,
    DashboardMinimalRoutes,
    {
      path: '*', // Catch-all unknown routes
      element: <Navigate to="/login" replace />,
    }
  ],
  {
    basename: import.meta.env.VITE_APP_BASE_NAME || '/'
  }
);

export default router;
