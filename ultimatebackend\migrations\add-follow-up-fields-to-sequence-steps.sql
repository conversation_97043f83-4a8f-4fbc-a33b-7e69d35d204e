-- Migration to add follow-up functionality to sequence_steps table
-- Run this SQL script to update your database schema

-- Add new columns for follow-up functionality
ALTER TABLE sequence_steps 
ADD COLUMN IF NOT EXISTS noResponseDelayHours INTEGER,
ADD COLUMN IF NOT EXISTS hasNoResponseFollowUp BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS parentStepId INTEGER,
ADD COLUMN IF NOT EXISTS followUpTrigger VARCHAR(20) DEFAULT 'DELAYED' CHECK (followUpTrigger IN ('IMMEDIATE', 'DELAYED', 'CONDITIONAL')),
ADD COLUMN IF NOT EXISTS followUpConditions TEXT;

-- Add foreign key constraint for parent step relationship
ALTER TABLE sequence_steps 
ADD CONSTRAINT fk_sequence_steps_parent 
FOREIGN KEY (parentStepId) REFERENCES sequence_steps(id) ON DELETE CASCADE;

-- Update the type enum to include NO_RESPONSE_FOLLOW_UP
ALTER TABLE sequence_steps 
ALTER COLUMN type TYPE VARCHAR(30);

-- Update existing records to use the new enum values if needed
UPDATE sequence_steps 
SET type = 'NO_RESPONSE_FOLLOW_UP' 
WHERE type = 'FOLLOW_UP' AND parentStepId IS NOT NULL;

-- Add comments to the new columns
COMMENT ON COLUMN sequence_steps.noResponseDelayHours IS 'Hours to wait before triggering follow-up if no response';
COMMENT ON COLUMN sequence_steps.hasNoResponseFollowUp IS 'Whether this step has follow-up actions for no response';
COMMENT ON COLUMN sequence_steps.parentStepId IS 'ID of the parent step for follow-up steps';
COMMENT ON COLUMN sequence_steps.followUpTrigger IS 'When to trigger the follow-up step (IMMEDIATE, DELAYED, CONDITIONAL)';
COMMENT ON COLUMN sequence_steps.followUpConditions IS 'JSON configuration for follow-up conditions';

-- Create index for better performance on follow-up queries
CREATE INDEX IF NOT EXISTS idx_sequence_steps_parent_step ON sequence_steps(parentStepId);
CREATE INDEX IF NOT EXISTS idx_sequence_steps_follow_up ON sequence_steps(hasNoResponseFollowUp) WHERE hasNoResponseFollowUp = TRUE;

-- Add a check constraint to ensure follow-up steps have a parent
ALTER TABLE sequence_steps 
ADD CONSTRAINT chk_follow_up_parent 
CHECK (
  (type != 'NO_RESPONSE_FOLLOW_UP') OR 
  (type = 'NO_RESPONSE_FOLLOW_UP' AND parentStepId IS NOT NULL)
);

-- Example data for testing (optional - remove in production)
-- INSERT INTO sequence_steps (name, "order", type, medium, "roleSequenceId", "noResponseDelayHours", "hasNoResponseFollowUp") 
-- VALUES ('Initial Email', 0, 'OUTREACH', 'EMAIL', 1, 24, TRUE);

-- INSERT INTO sequence_steps (name, "order", type, medium, "roleSequenceId", "parentStepId", "followUpTrigger") 
-- VALUES ('Follow-up Email', 1, 'NO_RESPONSE_FOLLOW_UP', 'EMAIL', 1, 1, 'DELAYED');

COMMIT;
