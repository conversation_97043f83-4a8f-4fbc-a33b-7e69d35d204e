import { Module } from '@nestjs/common';
import { SequenceService } from './sequence.service';
import { FollowUpService } from './follow-up.service';
import { SequenceController } from './sequence.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RoleSequence } from './sequence.entity';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { CandidateSequenceStatus } from 'src/candidate-sequence-status/candidate-sequence-status.entity';
import { CandidateSequenceStatusModule } from 'src/candidate-sequence-status/candidate-sequence-status.module';
import { QueueModule } from 'src/queue/queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      RoleSequence,
      SequenceSteps,
      RoleCandidate,
      People,
      PersonEmail,
      PersonPhone,
      CandidateSequenceStatus,
    ]),
    CandidateSequenceStatusModule,
    QueueModule,
  ],
  providers: [SequenceService, FollowUpService],
  controllers: [SequenceController],
  exports: [SequenceService, FollowUpService],
})
export class SequenceModule {}
