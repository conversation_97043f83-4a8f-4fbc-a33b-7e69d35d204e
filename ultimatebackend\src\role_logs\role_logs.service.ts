import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { RoleLogs } from './role_logs.entity';
import { IsNull, Not, Repository } from 'typeorm';
import { Roles } from 'src/roles/roles.entity';
import { RoleLogsDto } from './dto/roleLogs.dto';
import { UpdateRoleLogsDto } from './dto/updateRoleLogs.dto';
import {
  RoleLogsAction,
  RoleLogsStatus,
  RoleLogsType,
} from './dto/rol_los.enum';
import { GetTrialLogsByRoleIdDto } from './dto/role_logs.dto';
import { Users } from 'src/users/users.entity';

@Injectable()
export class RoleLogsService {
  constructor(
    @InjectRepository(RoleLogs)
    private readonly roleLogsRepository: Repository<RoleLogs>,
    @InjectRepository(Roles)
    private readonly rolesRepository: Repository<Roles>,
    @InjectRepository(Users)
    private readonly usersRepository: Repository<Users>,
  ) {}

  async createRoleLog(roleLog: RoleLogsDto): Promise<RoleLogs> {
    try {
      const role = await this.rolesRepository.findOneBy({ id: roleLog.roleId });
      if (!role) {
        throw new NotFoundException('Role not found');
      }

      const newRoleLog = this.roleLogsRepository.create(roleLog);
      newRoleLog.role = role;
      return await this.roleLogsRepository.save(newRoleLog);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error creating role log',
        error.message,
      );
    }
  }

  async updateRoleLog(
    id: number,
    updateRoleLogDto: UpdateRoleLogsDto,
  ): Promise<RoleLogs> {
    try {
      const roleLog = await this.roleLogsRepository.findOneBy({ id });
      if (!roleLog) {
        throw new NotFoundException('Role log not found');
      }

      //   calculate time spent
      if (updateRoleLogDto.start_time && updateRoleLogDto.end_time) {
        const startTime = new Date(updateRoleLogDto.start_time).getTime();
        const endTime = new Date(updateRoleLogDto.end_time).getTime();
        const timeSpent = endTime - startTime;
        updateRoleLogDto.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
      }

      Object.assign(roleLog, updateRoleLogDto);

      //  update the role current_status
      if (updateRoleLogDto.log_status_type) {
        const role = await this.rolesRepository.findOneBy({
          id: roleLog.roleId,
        });
        if (!role) {
          throw new NotFoundException('Role not found');
        }
        role.current_status =
          updateRoleLogDto.log_status_type +
          ' at ' +
          updateRoleLogDto.log_status_at;
        await this.rolesRepository.save(role);
      }
      return await this.roleLogsRepository.save(roleLog);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error updating role log',
        error.message,
      );
    }
  }

  async deleteRoleLog(id: number): Promise<void> {
    try {
      const result = await this.roleLogsRepository.delete(id);
      if (result.affected === 0) {
        throw new NotFoundException('Role log not found');
      }
    } catch (error) {
      throw new InternalServerErrorException(
        'Error deleting role log',
        error.message,
      );
    }
  }

  async getRoleLogs(): Promise<RoleLogs[]> {
    try {
      return await this.roleLogsRepository.find({ relations: ['role'] });
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching role logs',
        error.message,
      );
    }
  }

  async getRoleLogById(id: number): Promise<RoleLogs> {
    try {
      const roleLog = await this.roleLogsRepository.findOne({
        where: { id },
        relations: ['role'],
      });
      if (!roleLog) {
        throw new NotFoundException('Role log not found');
      }
      return roleLog;
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching role log',
        error.message,
      );
    }
  }

  // get by roleId, userId, and log status type
  async getRoleLogsByRoleIdAndUserId(
    roleId?: number,
    userId?: number,
  ): Promise<RoleLogs[]> {
    try {
      const query = this.roleLogsRepository
        .createQueryBuilder('roleLog')
        .leftJoinAndSelect('roleLog.role', 'role')
        .leftJoinAndSelect('roleLog.user', 'user');

      if (roleId) {
        query.andWhere('role.id = :roleId', { roleId });
      }

      if (userId) {
        query.andWhere('user.id = :userId', { userId });
      }

      return await query.getMany();
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching role logs',
        error.message,
      );
    }
  }

  async startRole(roleId: number, userId: string): Promise<RoleLogs> {
    try {
      // search user by userId
      if (!userId) {
        throw new BadRequestException('User ID is required');
      }
      const user = await this.usersRepository.findOneBy({
        id: userId,
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const role = await this.rolesRepository.findOneBy({ id: roleId });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      // update the last role log to end time
      const lastRoleLog = await this.roleLogsRepository.findOne({
        where: { roleId },
        order: { start_time: 'DESC' },
      });
      if (lastRoleLog) {
        lastRoleLog.end_time = new Date();
        const startTime = new Date(lastRoleLog.start_time).getTime();
        const endTime = new Date(lastRoleLog.end_time).getTime();
        const timeSpent = endTime - startTime;
        lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
        await this.roleLogsRepository.save(lastRoleLog);
      }
      // create a new role log

      const newRoleLog = this.roleLogsRepository.create({
        action: RoleLogsAction.STARTED,
        timestamp: new Date(),
        details: `Started ${role.title} role`,
        role_number: role.role_number,
        log_status_type: RoleLogsStatus.IN_PROGRESS,
        log_status_at: RoleLogsType.RESOURCER,
        comment: `Started ${role.title} role`,
        time_spent: '0 minutes',
        status: RoleLogsStatus.IN_PROGRESS,
        next_stage:
          RoleLogsStatus.IN_PROGRESS + ' at ' + RoleLogsType.RESOURCER,
        start_time: new Date(),
        role_date: lastRoleLog.role_date,
        roleId: role.id,
        userId: userId,
      });

      // set the role current_status
      role.current_status =
        RoleLogsStatus.IN_PROGRESS + ' at ' + RoleLogsType.RESOURCER;
      await this.rolesRepository.save(role);
      return await this.roleLogsRepository.save(newRoleLog);
    } catch (error) {
      throw new InternalServerErrorException(
        error.message || 'Error starting role log',
      );
    }
  }

  async MarkRoleDone(roleId: number, userId: string): Promise<RoleLogs> {
    try {
      const role = await this.rolesRepository.findOneBy({ id: roleId });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      // update the last role log to end time
      const lastRoleLog = await this.roleLogsRepository.findOne({
        where: { roleId },
        order: { start_time: 'DESC' },
      });
      if (lastRoleLog) {
        lastRoleLog.end_time = new Date();
        // calculate time spent
        const startTime = new Date(lastRoleLog.start_time).getTime();
        const endTime = new Date(lastRoleLog.end_time).getTime();
        const timeSpent = endTime - startTime;
        lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
        await this.roleLogsRepository.save(lastRoleLog);
      }
      // create a new role log

      const newRoleLog = this.roleLogsRepository.create({
        action: RoleLogsAction.DONE,
        timestamp: new Date(),
        details: `Marked ${role.title} role Done`,
        role_number: role.role_number,
        log_status_type: RoleLogsStatus.DONE,
        log_status_at: RoleLogsType.RESOURCER,
        comment: `Mark ${role.title} role Done`,
        time_spent: '0 minutes',
        next_stage: RoleLogsStatus.PENDING + ' at ' + RoleLogsType.QA,
        status: RoleLogsStatus.DONE,
        start_time: new Date(),
        roleId: role.id,
        userId: userId,
      });

      // set the role current_status
      role.current_status =
        RoleLogsStatus.DONE + ' at ' + RoleLogsType.RESOURCER;
      await this.rolesRepository.save(role);
      return await this.roleLogsRepository.save(newRoleLog);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error starting role log',
        error.message,
      );
    }
  }

  async MarkRoleLeft(roleId: number, userId: string): Promise<RoleLogs> {
    try {
      const role = await this.rolesRepository.findOneBy({ id: roleId });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      // update the last role log to end time
      const lastRoleLog = await this.roleLogsRepository.findOne({
        where: { roleId },
        order: { start_time: 'DESC' },
      });
      if (lastRoleLog) {
        lastRoleLog.end_time = new Date();
        // calculate time spent
        const startTime = new Date(lastRoleLog.start_time).getTime();
        const endTime = new Date(lastRoleLog.end_time).getTime();
        const timeSpent = endTime - startTime;
        lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
        await this.roleLogsRepository.save(lastRoleLog);
      }
      // create a new role log

      const newRoleLog = this.roleLogsRepository.create({
        action: RoleLogsAction.CANCELLED,

        timestamp: new Date(),
        details: `Marked ${role.title} role Left`,
        role_number: role.role_number,
        log_status_type: RoleLogsStatus.CANCELLED,
        log_status_at: RoleLogsType.RESOURCER,
        comment: `Mark ${role.title} role Left`,
        time_spent: '0 minutes',
        next_stage: RoleLogsStatus.PENDING + ' at ' + RoleLogsType.RESOURCER,
        status: RoleLogsStatus.CANCELLED,
        start_time: new Date(),
        roleId: role.id,
        userId: userId,
        role_date: lastRoleLog.role_date,
      });

      // set the role current_status
      role.current_status =
        RoleLogsStatus.CANCELLED + ' at ' + RoleLogsType.RESOURCER;
      await this.rolesRepository.save(role);
      return await this.roleLogsRepository.save(newRoleLog);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error starting role log',
        error.message,
      );
    }
  }

  async markRoleChecked(roleId: number, userId: string): Promise<RoleLogs> {
    try {
      const role = await this.rolesRepository.findOneBy({ id: roleId });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      // update the last role log to end time
      const lastRoleLog = await this.roleLogsRepository.findOne({
        where: { roleId },
        order: { start_time: 'DESC' },
      });
      if (lastRoleLog) {
        lastRoleLog.end_time = new Date();
        // calculate time spent
        const startTime = new Date(lastRoleLog.start_time).getTime();
        const endTime = new Date(lastRoleLog.end_time).getTime();
        const timeSpent = endTime - startTime;
        lastRoleLog.time_spent = `${Math.floor(timeSpent / 1000 / 60)} minutes`;
        await this.roleLogsRepository.save(lastRoleLog);
      }
      // create a new role log

      const newRoleLog = this.roleLogsRepository.create({
        action: RoleLogsAction.CHECKED,
        timestamp: new Date(),
        details: `Marked ${role.title} role Checked`,
        role_number: role.role_number,
        log_status_type: RoleLogsStatus.APPROVED,
        log_status_at: RoleLogsType.QA,
        comment: `Mark ${role.title} role Checked`,
        time_spent: '0 minutes',
        next_stage: RoleLogsStatus.PENDING + ' at ' + RoleLogsType.ACM,
        status: RoleLogsStatus.APPROVED,
        start_time: new Date(),
        roleId: role.id,
        userId: userId,
        role_date: lastRoleLog.role_date,
      });

      // set the role current_status
      role.current_status = RoleLogsStatus.APPROVED + ' at ' + RoleLogsType.QA;
      await this.rolesRepository.save(role);
      return await this.roleLogsRepository.save(newRoleLog);
    } catch (error) {
      throw new InternalServerErrorException(
        'Error starting role log',
        error.message,
      );
    }
  }

  async getRoleLogsByRoleId(roleId: number): Promise<RoleLogs[]> {
    try {
      const role = await this.rolesRepository.findOneBy({ id: roleId });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      return await this.roleLogsRepository.find({
        where: { roleId },
        relations: ['role', 'user'],
        order: { start_time: 'DESC' },
      });
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching role logs',
        error.message,
      );
    }
  }

  async getTotalTrialLogs(queryParams: GetTrialLogsByRoleIdDto): Promise<{
    cvsourcingRoles: RoleLogs[];
    preQualificationRoles: RoleLogs[];
    directRoles: RoleLogs[];
    trialRoles: RoleLogs[];
  }> {
    const {
      roleNumber,
      clientNumber,
      roleDate,
      endDate,
      isAdvance,
      isPrevious,
      bdUserId,
      serviceId,
      userId,
      acmUserId,
      roleId,
      searchString,
      page,
      pageSize,
    } = queryParams;

    const pageInt = parseInt(page) || 0;
    const pageSizeInt = parseInt(pageSize) || 10;

    const role_date =
      typeof roleDate === 'string' ? roleDate.trim() : undefined;
    const end_date = typeof endDate === 'string' ? endDate.trim() : undefined;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    try {
      if (isAdvance === true && isPrevious === true) {
        throw new BadRequestException({
          message: 'isAdvance and isPrevious cannot be both true',
        });
      }

      const queryBuilder = this.roleLogsRepository
        .createQueryBuilder('roleLog')
        .leftJoinAndSelect('roleLog.role', 'role')
        .leftJoinAndSelect('role.person', 'person')
        .leftJoinAndSelect('roleLog.user', 'user')
        .leftJoinAndSelect('role.service', 'service');

      // queryBuilder.andWhere('role.category = :category', {
      //   category: 'TRIAL',
      // });
      if (roleNumber !== undefined) {
        queryBuilder.andWhere('roleLog.role_number = :roleNumber', {
          roleNumber,
        });
      }

      if (clientNumber !== undefined) {
        queryBuilder.andWhere('person.client_number = :clientNumber', {
          clientNumber,
        });
      }

      if (isValidDate(role_date) && isValidDate(end_date)) {
        queryBuilder.andWhere('roleLog.role_date BETWEEN :start AND :end', {
          start: isValidDate(role_date),
          end: isValidDate(end_date),
        });
      } else if (role_date) {
        queryBuilder.andWhere('roleLog.role_date = :role_date', {
          role_date,
        });
      }

      if (isAdvance === true) {
        queryBuilder.andWhere('roleLog.role_date > :currentDate', {
          currentDate: new Date(),
        });
      }

      if (isPrevious === true) {
        queryBuilder.andWhere('roleLog.role_date < :currentDate', {
          currentDate: new Date(),
        });
      }

      if (bdUserId) {
        queryBuilder.andWhere('role.bdUserId = :bdUserId', { bdUserId });
      }
      if (serviceId) {
        queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId });
      }
      if (userId) {
        queryBuilder.andWhere('role.userId = :userId', { userId });
      }
      if (acmUserId) {
        queryBuilder.andWhere('role.acmUserId = :acmUserId', { acmUserId });
      }
      if (roleId) {
        queryBuilder.andWhere('role.id = :roleId', { roleId });
      }
      if (searchString) {
        queryBuilder.andWhere('(role.title ILIKE :searchString)', {
          searchString: `%${searchString}%`,
        });
      }

      const roleLogs = await queryBuilder.getMany();

      // group each role by roleId and return only the latest role log
      const groupedRoleLogs = roleLogs.reduce(
        (acc, roleLog) => {
          if (!acc[roleLog.roleId]) {
            acc[roleLog.roleId] = roleLog;
          } else {
            const existingRoleLog = acc[roleLog.roleId];
            if (roleLog.start_time > existingRoleLog.start_time) {
              acc[roleLog.roleId] = roleLog;
            }
          }
          return acc;
        },
        {} as Record<number, RoleLogs>,
      );
      const roleLogsArray = Object.values(groupedRoleLogs);
      // sort by start_time
      roleLogsArray.sort((a, b) => {
        return (
          new Date(b.start_time).getTime() - new Date(a.start_time).getTime()
        );
      });

      const paginate = (data: RoleLogs[]) => {
        const start = pageInt * pageSizeInt;
        const end = start + pageSizeInt;
        return data.slice(start, end);
      };

      const cvsourcingRoles = paginate(
        roleLogsArray.filter(
          (r) => r.role.serviceId === 1 && r.role.category !== 'TRIAL',
        ),
      );
      const preQualificationRoles = paginate(
        roleLogsArray.filter(
          (r) => r.role.serviceId === 2 && r.role.category !== 'TRIAL',
        ),
      );
      const directRoles = paginate(
        roleLogsArray.filter(
          (r) => r.role.serviceId === 3 && r.role.category !== 'TRIAL',
        ),
      );
      const trialRoles = paginate(
        roleLogsArray.filter((r) => r.role.category === 'TRIAL'),
      );

      return {
        cvsourcingRoles,
        preQualificationRoles,
        directRoles,
        trialRoles,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error(
        'An error occurred while fetching role logs: ' + error.message,
      );
    }
  }

  async getRoleActivityByRoleId(roleId: number): Promise<RoleLogs[]> {
    try {
      console.log('Fetching role activity for roleId:', roleId);
      const roleLogs = await this.roleLogsRepository.find({
        where: { roleId },
        relations: ['role', 'user'],
        order: { start_time: 'DESC' },
      });
      return roleLogs ?? [];
    } catch (error) {
      // Re-throw or handle DB error as needed
      throw new InternalServerErrorException(
        'Error fetching role activity',
        error.message,
      );
    }
  }

  // async changeRoleLogStatus(
  //   roleLogId: number,
  //   status: string,
  // ): Promise<RoleLogs> {
  //   try {
  //     const statusConfig = {
  //       RESUMED: {
  //         status: RoleLogsStatus.IN_PROGRESS,
  //         comment: 'Resumed role log',
  //         action: RoleLogsAction.RESUMED,
  //         detailsPrefix: 'Resumed',
  //       },
  //       PAUSED: {
  //         status: RoleLogsStatus.ON_HOLD,
  //         comment: 'Paused role log',
  //         action: RoleLogsAction.PAUSED,
  //         detailsPrefix: 'Paused',
  //       },
  //       ONHOLD: {
  //         status: RoleLogsStatus.ON_HOLD,
  //         comment: 'On hold role log',
  //         action: RoleLogsAction.UNASSIGN,
  //         detailsPrefix: 'On hold',
  //       },
  //       ISSUE: {
  //         status: RoleLogsStatus.REJECTED,
  //         comment: 'Issue role log',
  //         action: RoleLogsAction.UNASSIGN,
  //         detailsPrefix: 'Issue',
  //       },
  //     };

  //     const config = statusConfig[status];
  //     if (!config) {
  //       throw new BadRequestException('Invalid status');
  //     }

  //     const roleLog = await this.roleLogsRepository.findOne({
  //       where: { id: roleLogId },
  //       relations: ['role'], // preload related role
  //     });

  //     if (!roleLog) {
  //       throw new NotFoundException('Role log not found');
  //     }

  //     const role = await this.rolesRepository.findOneBy({
  //       id: roleLog.roleId,
  //     });

  //     if (!role) {
  //       throw new NotFoundException('Role not found');
  //     }

  //     const now = new Date();
  //     const newStatusStage = `${config.status} at ${RoleLogsType.ACM}`;

  //     Object.assign(roleLog, {
  //       status: config.status,
  //       next_stage: newStatusStage,
  //       log_status_type: config.status,
  //       log_status_at: RoleLogsType.ACM,
  //       comment: config.comment,
  //       time_spent: '0 minutes',
  //       start_time: now,
  //       end_time: null,
  //       action: config.action,
  //       timestamp: now,
  //       details: `${config.detailsPrefix} ${roleLog.role.title} role`,
  //     });

  //     role.current_status = newStatusStage;

  //     await this.rolesRepository.save(role);
  //     return await this.roleLogsRepository.save(roleLog);
  //   } catch (error) {
  //     throw new InternalServerErrorException(
  //       'Error changing role log status',
  //       error.message,
  //     );
  //   }
  // }

  async changeRoleLogStatus(
    roleLogId: number,
    status: string,
  ): Promise<RoleLogs> {
    try {
      switch (status) {
        case 'RESUMED':
          // update role log status to RESUMED
          const resumedRoleLog = await this.roleLogsRepository.findOneBy({
            id: roleLogId,
          });
          if (!resumedRoleLog) {
            throw new NotFoundException('Role log not found');
          }
          resumedRoleLog.status = RoleLogsStatus.IN_PROGRESS;
          resumedRoleLog.next_stage =
            RoleLogsStatus.IN_PROGRESS + ' at ' + RoleLogsType.ACM;
          resumedRoleLog.log_status_type = RoleLogsStatus.IN_PROGRESS;
          resumedRoleLog.log_status_at = RoleLogsType.ACM;
          resumedRoleLog.comment = 'Resumed role log';
          resumedRoleLog.time_spent = '0 minutes';
          resumedRoleLog.start_time = new Date();
          resumedRoleLog.end_time = null;
          resumedRoleLog.role_date = resumedRoleLog.role_date;
          resumedRoleLog.action = RoleLogsAction.RESUMED;
          resumedRoleLog.timestamp = new Date();
          resumedRoleLog.roleId = resumedRoleLog.roleId;
          resumedRoleLog.userId = resumedRoleLog.userId;
          resumedRoleLog.role_number = resumedRoleLog.role_number;
          resumedRoleLog.details = 'Resumed ${resumedRoleLog.role.title} role';

          //  also update role current status
          const role = await this.rolesRepository.findOneBy({
            id: resumedRoleLog.roleId,
          });
          if (!role) {
            throw new NotFoundException('Role not found');
          }
          role.current_status =
            RoleLogsStatus.IN_PROGRESS + ' at ' + RoleLogsType.ACM;
          await this.rolesRepository.save(role);
          return await this.roleLogsRepository.save(resumedRoleLog);

        case 'PAUSED':
          // update role log status to PAUSED
          const pausedRoleLog = await this.roleLogsRepository.findOneBy({
            id: roleLogId,
          });
          if (!pausedRoleLog) {
            throw new NotFoundException('Role log not found');
          }
          pausedRoleLog.status = RoleLogsStatus.ON_HOLD;
          pausedRoleLog.next_stage =
            RoleLogsStatus.ON_HOLD + ' at ' + RoleLogsType.ACM;
          pausedRoleLog.log_status_type = RoleLogsStatus.ON_HOLD;
          pausedRoleLog.log_status_at = RoleLogsType.ACM;
          pausedRoleLog.comment = 'Paused role log';
          pausedRoleLog.time_spent = '0 minutes';
          pausedRoleLog.start_time = new Date();
          pausedRoleLog.end_time = null;
          pausedRoleLog.role_date = pausedRoleLog.role_date;
          pausedRoleLog.action = RoleLogsAction.PAUSED;
          pausedRoleLog.timestamp = new Date();
          pausedRoleLog.roleId = pausedRoleLog.roleId;
          pausedRoleLog.userId = pausedRoleLog.userId;
          pausedRoleLog.role_number = pausedRoleLog.role_number;
          pausedRoleLog.details = 'Paused ${pausedRoleLog.role.title} role';
          //  also update role current status
          const pausedRole = await this.rolesRepository.findOneBy({
            id: pausedRoleLog.roleId,
          });
          if (!pausedRole) {
            throw new NotFoundException('Role not found');
          }
          pausedRole.current_status =
            RoleLogsStatus.ON_HOLD + ' at ' + RoleLogsType.ACM;
          await this.rolesRepository.save(pausedRole);
          return await this.roleLogsRepository.save(pausedRoleLog);

        case 'ONHOLD':
          // update role log status to ONHOLD
          const onHoldRoleLog = await this.roleLogsRepository.findOneBy({
            id: roleLogId,
          });
          if (!onHoldRoleLog) {
            throw new NotFoundException('Role log not found');
          }
          onHoldRoleLog.status = RoleLogsStatus.ON_HOLD;
          onHoldRoleLog.next_stage =
            RoleLogsStatus.ON_HOLD + ' at ' + RoleLogsType.ACM;
          onHoldRoleLog.log_status_type = RoleLogsStatus.ON_HOLD;
          onHoldRoleLog.log_status_at = RoleLogsType.ACM;
          onHoldRoleLog.comment = 'On hold role log';
          onHoldRoleLog.time_spent = '0 minutes';
          onHoldRoleLog.start_time = new Date();
          onHoldRoleLog.end_time = null;
          onHoldRoleLog.role_date = onHoldRoleLog.role_date;
          onHoldRoleLog.action = RoleLogsAction.UNASSIGN;
          onHoldRoleLog.timestamp = new Date();
          onHoldRoleLog.roleId = onHoldRoleLog.roleId;
          onHoldRoleLog.userId = onHoldRoleLog.userId;
          onHoldRoleLog.role_number = onHoldRoleLog.role_number;
          onHoldRoleLog.details = 'On hold ${onHoldRoleLog.role.title} role';
          //  also update role current status
          const holdedRole = await this.rolesRepository.findOneBy({
            id: onHoldRoleLog.roleId,
          });
          if (!holdedRole) {
            throw new NotFoundException('Role not found');
          }
          holdedRole.current_status =
            RoleLogsStatus.ON_HOLD + ' at ' + RoleLogsType.ACM;
          await this.rolesRepository.save(holdedRole);
          return await this.roleLogsRepository.save(onHoldRoleLog);
        case 'ISSUE':
          // update role log status to ISSUE
          const issueRoleLog = await this.roleLogsRepository.findOneBy({
            id: roleLogId,
          });
          if (!issueRoleLog) {
            throw new NotFoundException('Role log not found');
          }
          issueRoleLog.status = RoleLogsStatus.REJECTED;
          issueRoleLog.next_stage =
            RoleLogsStatus.REJECTED + ' at ' + RoleLogsType.ACM;
          issueRoleLog.log_status_type = RoleLogsStatus.REJECTED;
          issueRoleLog.log_status_at = RoleLogsType.ACM;
          issueRoleLog.comment = 'Issue role log';
          issueRoleLog.time_spent = '0 minutes';
          issueRoleLog.start_time = new Date();
          issueRoleLog.end_time = null;
          issueRoleLog.role_date = issueRoleLog.role_date;
          issueRoleLog.action = RoleLogsAction.UNASSIGN;
          issueRoleLog.timestamp = new Date();
          issueRoleLog.roleId = issueRoleLog.roleId;
          issueRoleLog.userId = issueRoleLog.userId;
          issueRoleLog.role_number = issueRoleLog.role_number;
          issueRoleLog.details = 'Issue ${issueRoleLog.role.title} role';
          //  also update role current status
          const issuedRole = await this.rolesRepository.findOneBy({
            id: issueRoleLog.roleId,
          });
          if (!issuedRole) {
            throw new NotFoundException('Role not found');
          }
          issuedRole.current_status =
            RoleLogsStatus.REJECTED + ' at ' + RoleLogsType.ACM;
          await this.rolesRepository.save(issuedRole);
          return await this.roleLogsRepository.save(issueRoleLog);
      }
    } catch (error) {
      throw new InternalServerErrorException(
        'Error changing role log status',
        error.message,
      );
    }
  }

  async recentRoleActivityBdDashboard(userId: string) {
    try {
      return await this.roleLogsRepository
        .createQueryBuilder('roleLog')
        .leftJoinAndSelect('roleLog.user', 'user')
        .where('roleLog.start_time IS NOT NULL')
        .andWhere('user.id = :userId', { userId })
        .orderBy('roleLog.start_time', 'DESC')
        .take(5)
        .select(['roleLog', 'user.id', 'user.first_name', 'user.last_name'])
        .getMany();
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching role activity',
        error.message,
      );
    }
  }
}
