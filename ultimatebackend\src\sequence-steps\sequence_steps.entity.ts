import { EmailTemplates } from 'src/email-templates/emailTemplates.entity';
import { RoleSequence } from 'src/sequence/sequence.entity';
import { Column, Entity, ManyToOne, PrimaryGeneratedColumn, OneToMany } from 'typeorm';

@Entity('sequence_steps')
export class SequenceSteps {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', nullable: true })
  name: string;

  @Column({
    type: 'int',
    nullable: true,
    default: 0,
  })
  order: number;

  @Column({
    type: 'enum',
    enum: ['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'NO_RESPONSE_FOLLOW_UP', 'OTHER'],
    default: 'OUTREACH',
  })
  type: string;

  @Column({
    type: 'enum',
    enum: ['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER'],
    default: 'EMAIL',
  })
  medium: string;

  @ManyToOne(
    () => EmailTemplates,
    (emailTemplate) => emailTemplate.sequenceSteps,
    { nullable: true },
  )
  emailTemplate: EmailTemplates;

  @Column({
    nullable: true,
  })
  templateId: number;

  @ManyToOne(() => RoleSequence, (roleSequence) => roleSequence.sequenceSteps, {
    nullable: true,
  })
  roleSequence: RoleSequence;

  @Column({
    nullable: true,
  })
  roleSequenceId: number;

  // Follow-up configuration for no response
  @Column({
    type: 'int',
    nullable: true,
    comment: 'Hours to wait before triggering follow-up if no response'
  })
  noResponseDelayHours: number;

  @Column({
    type: 'boolean',
    default: false,
    comment: 'Whether this step has follow-up actions for no response'
  })
  hasNoResponseFollowUp: boolean;

  // Self-referencing relationship for follow-up steps
  @ManyToOne(() => SequenceSteps, (step) => step.followUpSteps, { nullable: true })
  parentStep: SequenceSteps;

  @Column({ nullable: true })
  parentStepId: number;

  @OneToMany(() => SequenceSteps, (step) => step.parentStep)
  followUpSteps: SequenceSteps[];

  @Column({
    type: 'enum',
    enum: ['IMMEDIATE', 'DELAYED', 'CONDITIONAL'],
    default: 'DELAYED',
    comment: 'When to trigger the follow-up step'
  })
  followUpTrigger: string;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'JSON configuration for follow-up conditions'
  })
  followUpConditions: string;
}
