import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { EmailService } from '../email/email.service';
import { S3bucketService } from '../s3bucket/s3bucket.service';
import { CandidateApplicationDto } from './dto/candidate-application.dto';

@Injectable()
export class CandidateApplicationService {
  constructor(
    private readonly emailService: EmailService,
    private readonly s3bucketService: S3bucketService,
  ) {}

  async submitApplication(
    applicationData: CandidateApplicationDto,
    resumeFile?: Express.Multer.File,
  ): Promise<{ message: string; applicationId: string }> {
    try {
      // Generate unique application ID
      const applicationId = `APP_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Upload resume file to S3 if provided
      let resumeUrl: string | undefined = undefined;
      if (resumeFile) {
        resumeUrl = await this.s3bucketService.uploadFile(resumeFile);
      }

      // Prepare email content
      const fullName = `${applicationData.first_name} ${applicationData.last_name}`;
      const subject = `New Candidate Application - ${applicationData.desired_job_role}`;

      const emailBody = this.generateEmailBody(applicationData, fullName, applicationId, resumeUrl);

      // Prepare email recipients
      const recipients = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      // Send email with or without attachment
      if (resumeFile && resumeUrl) {
        await this.emailService.sendEmailWithAttachments({
          to: recipients,
          subject,
          body: emailBody,
          from: process.env.DEFAULT_FROM_EMAIL,
          fileUrls: [resumeUrl],
        });
      } else {
        await this.emailService.sendEmail({
          to: recipients,
          subject,
          body: emailBody,
          from: process.env.DEFAULT_FROM_EMAIL,
        });
      }

      // Send confirmation email to candidate
      await this.sendConfirmationEmail(applicationData, fullName, applicationId);

      return {
        message: 'Application submitted successfully',
        applicationId,
      };
    } catch (error) {
      console.error('Error submitting candidate application:', error);
      throw new InternalServerErrorException({
        message: 'Failed to submit application. Please try again.',
        error: error.message,
      });
    }
  }

  private generateEmailBody(
    applicationData: CandidateApplicationDto,
    fullName: string,
    applicationId: string,
    resumeUrl?: string,
  ): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
          New Candidate Application Received
        </h2>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="color: #2c3e50; margin-top: 0;">Application Details</h3>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Application ID:</td>
              <td style="padding: 8px; color: #2c3e50;">${applicationId}</td>
            </tr>
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Full Name:</td>
              <td style="padding: 8px; color: #2c3e50;">${fullName}</td>
            </tr>
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Email:</td>
              <td style="padding: 8px; color: #2c3e50;">${applicationData.contact}</td>
            </tr>
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Desired Role:</td>
              <td style="padding: 8px; color: #2c3e50;">${applicationData.desired_job_role}</td>
            </tr>
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Job Location:</td>
              <td style="padding: 8px; color: #2c3e50;">${applicationData.job_location}</td>
            </tr>
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Experience:</td>
              <td style="padding: 8px; color: #2c3e50;">${applicationData.years_of_experience}</td>
            </tr>
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Availability:</td>
              <td style="padding: 8px; color: #2c3e50;">${applicationData.availability}</td>
            </tr>
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Reason for Change:</td>
              <td style="padding: 8px; color: #2c3e50;">${applicationData.reason_for_job_change}</td>
            </tr>
            ${applicationData.linkedin_profile ? `
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">LinkedIn:</td>
              <td style="padding: 8px; color: #2c3e50;">
                <a href="${applicationData.linkedin_profile}" target="_blank" style="color: #3498db;">
                  ${applicationData.linkedin_profile}
                </a>
              </td>
            </tr>
            ` : ''}
            ${resumeUrl ? `
            <tr>
              <td style="padding: 8px; font-weight: bold; color: #34495e;">Resume:</td>
              <td style="padding: 8px; color: #2c3e50;">
                <a href="${resumeUrl}" target="_blank" style="color: #3498db;">
                  View Resume (Attached)
                </a>
              </td>
            </tr>
            ` : ''}
          </table>
        </div>

        <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0; color: #2c3e50;">
            <strong>Submitted:</strong> ${new Date().toLocaleString('en-GB', {
              timeZone: 'Europe/London',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>

        <p style="color: #7f8c8d; font-size: 12px; margin-top: 30px;">
          This email was automatically generated by the Ultimate Outsourcing Career Portal.
        </p>
      </div>
    `;
  }

  private async sendConfirmationEmail(
    applicationData: CandidateApplicationDto,
    fullName: string,
    applicationId: string,
  ): Promise<void> {
    const subject = 'Application Received - Ultimate Outsourcing';
    const confirmationBody = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
          Thank You for Your Application!
        </h2>

        <p style="color: #2c3e50; font-size: 16px;">Dear ${fullName},</p>

        <p style="color: #2c3e50; line-height: 1.6;">
          Thank you for submitting your application for the <strong>${applicationData.desired_job_role}</strong> position.
          We have successfully received your application and our recruitment team will review it shortly.
        </p>

        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="color: #2c3e50; margin-top: 0;">Application Summary</h3>
          <p style="margin: 5px 0; color: #2c3e50;"><strong>Application ID:</strong> ${applicationId}</p>
          <p style="margin: 5px 0; color: #2c3e50;"><strong>Position:</strong> ${applicationData.desired_job_role}</p>
          <p style="margin: 5px 0; color: #2c3e50;"><strong>Location:</strong> ${applicationData.job_location}</p>
          <p style="margin: 5px 0; color: #2c3e50;"><strong>Submitted:</strong> ${new Date().toLocaleString('en-GB', {
            timeZone: 'Europe/London',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}</p>
        </div>

        <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <h3 style="color: #2c3e50; margin-top: 0;">What Happens Next?</h3>
          <ul style="color: #2c3e50; line-height: 1.6;">
            <li>Our recruitment team will review your application within 2-3 business days</li>
            <li>If your profile matches our requirements, we will contact you for an initial discussion</li>
            <li>You can use your Application ID (${applicationId}) for any future correspondence</li>
          </ul>
        </div>

        <p style="color: #2c3e50; line-height: 1.6;">
          If you have any questions about your application, please don't hesitate to contact us at
          <a href="mailto:<EMAIL>" style="color: #3498db;"><EMAIL></a>
        </p>

        <p style="color: #2c3e50; line-height: 1.6;">
          Best regards,<br>
          <strong>Ultimate Outsourcing Recruitment Team</strong>
        </p>

        <p style="color: #7f8c8d; font-size: 12px; margin-top: 30px;">
          This is an automated confirmation email. Please do not reply to this email.
        </p>
      </div>
    `;

    await this.emailService.sendEmail({
      to: [applicationData.contact],
      subject,
      body: confirmationBody,
      from: process.env.DEFAULT_FROM_EMAIL,
    });
  }
}