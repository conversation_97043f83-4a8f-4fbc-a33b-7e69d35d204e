import { Get, Post } from './api';

const BASE_URL = 'http://localhost:5001/api';

// Sequence Analytics API
export const sequenceAnalyticsAPI = {
  // Get comprehensive sequence statistics
  getSequenceStats: async (sequenceId) => {
    try {
      const response = await Get(`${BASE_URL}/sequence/${sequenceId}/stats`);
      return response;
    } catch (error) {
      console.error('Error fetching sequence stats:', error);
      throw error;
    }
  },

  // Get all sequences with basic stats
  getAllSequencesWithStats: async () => {
    try {
      const sequences = await Get(`${BASE_URL}/sequence/get-all`);
      const sequencesData = sequences.data || sequences;
      
      // Fetch stats for each sequence
      const sequencesWithStats = await Promise.all(
        sequencesData.map(async (sequence) => {
          try {
            const stats = await Get(`${BASE_URL}/sequence/${sequence.id}/stats`);
            return {
              ...sequence,
              stats: stats || {
                total: 0,
                sent: 0,
                delivered: 0,
                opened: 0,
                replied: 0,
                failed: 0,
                completed: 0
              }
            };
          } catch (error) {
            console.error(`Error fetching stats for sequence ${sequence.id}:`, error);
            return {
              ...sequence,
              stats: {
                total: 0,
                sent: 0,
                delivered: 0,
                opened: 0,
                replied: 0,
                failed: 0,
                completed: 0
              }
            };
          }
        })
      );
      
      return sequencesWithStats;
    } catch (error) {
      console.error('Error fetching sequences with stats:', error);
      throw error;
    }
  },

  // Get candidate journeys for a sequence
  getCandidateJourneys: async (sequenceId) => {
    try {
      const response = await Get(`${BASE_URL}/candidate-sequence-status/sequence/${sequenceId}`);
      return response || [];
    } catch (error) {
      console.error('Error fetching candidate journeys:', error);
      return [];
    }
  },

  // Get individual candidate journey
  getCandidateJourney: async (candidateId, sequenceId) => {
    try {
      const response = await Get(`${BASE_URL}/candidate-sequence-status/candidate/${candidateId}/sequence/${sequenceId}`);
      return response;
    } catch (error) {
      console.error('Error fetching candidate journey:', error);
      throw error;
    }
  },

  // Get sequence performance over time
  getSequencePerformanceTrends: async (sequenceId, dateRange) => {
    try {
      const { startDate, endDate } = dateRange;
      const response = await Get(`${BASE_URL}/sequence/${sequenceId}/performance-trends`, {
        params: { startDate, endDate }
      });
      return response;
    } catch (error) {
      console.error('Error fetching performance trends:', error);
      return [];
    }
  },

  // Get step-by-step breakdown
  getStepBreakdown: async (sequenceId) => {
    try {
      const response = await Get(`${BASE_URL}/sequence/${sequenceId}/step-breakdown`);
      return response;
    } catch (error) {
      console.error('Error fetching step breakdown:', error);
      return [];
    }
  },

  // Get response analytics
  getResponseAnalytics: async (sequenceId) => {
    try {
      const response = await Get(`${BASE_URL}/sequence/${sequenceId}/response-analytics`);
      return response;
    } catch (error) {
      console.error('Error fetching response analytics:', error);
      return {};
    }
  },

  // Get follow-up effectiveness
  getFollowUpEffectiveness: async (sequenceId) => {
    try {
      const response = await Get(`${BASE_URL}/sequence/${sequenceId}/followup-effectiveness`);
      return response;
    } catch (error) {
      console.error('Error fetching follow-up effectiveness:', error);
      return {};
    }
  }
};

// Sequence Management API
export const sequenceManagementAPI = {
  // Start sequence with role candidates
  startSequence: async (sequenceId, candidateIds = null) => {
    try {
      const endpoint = candidateIds 
        ? `${BASE_URL}/sequence/${sequenceId}/start`
        : `${BASE_URL}/sequence/${sequenceId}/start-with-role-candidates`;
      
      const payload = candidateIds ? { candidateIds } : {};
      const response = await Post(endpoint, payload);
      return response;
    } catch (error) {
      console.error('Error starting sequence:', error);
      throw error;
    }
  },

  // Pause sequence
  pauseSequence: async (sequenceId) => {
    try {
      const response = await Post(`${BASE_URL}/sequence/${sequenceId}/pause`, {});
      return response;
    } catch (error) {
      console.error('Error pausing sequence:', error);
      throw error;
    }
  },

  // Stop sequence
  stopSequence: async (sequenceId) => {
    try {
      const response = await Post(`${BASE_URL}/sequence/${sequenceId}/stop`, {});
      return response;
    } catch (error) {
      console.error('Error stopping sequence:', error);
      throw error;
    }
  },

  // Resume sequence
  resumeSequence: async (sequenceId) => {
    try {
      const response = await Post(`${BASE_URL}/sequence/${sequenceId}/resume`, {});
      return response;
    } catch (error) {
      console.error('Error resuming sequence:', error);
      throw error;
    }
  }
};

// Real-time Updates API
export const realTimeAPI = {
  // Get live sequence status
  getLiveSequenceStatus: async (sequenceId) => {
    try {
      const response = await Get(`${BASE_URL}/sequence/${sequenceId}/live-status`);
      return response;
    } catch (error) {
      console.error('Error fetching live status:', error);
      return {};
    }
  },

  // Get queue statistics
  getQueueStats: async () => {
    try {
      const response = await Get(`${BASE_URL}/queue/stats`);
      return response;
    } catch (error) {
      console.error('Error fetching queue stats:', error);
      return {};
    }
  },

  // Get recent activities
  getRecentActivities: async (limit = 50) => {
    try {
      const response = await Get(`${BASE_URL}/sequence/recent-activities`, {
        params: { limit }
      });
      return response;
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      return [];
    }
  }
};

// Dashboard Data API
export const dashboardAPI = {
  // Get dashboard overview data
  getDashboardOverview: async () => {
    try {
      const [sequences, queueStats, recentActivities] = await Promise.all([
        sequenceAnalyticsAPI.getAllSequencesWithStats(),
        realTimeAPI.getQueueStats(),
        realTimeAPI.getRecentActivities(20)
      ]);

      return {
        sequences,
        queueStats,
        recentActivities,
        summary: {
          totalSequences: sequences.length,
          activeSequences: sequences.filter(s => s.status === 'ACTIVE').length,
          totalCandidates: sequences.reduce((sum, s) => sum + (s.stats?.total || 0), 0),
          totalResponses: sequences.reduce((sum, s) => sum + (s.stats?.replied || 0), 0)
        }
      };
    } catch (error) {
      console.error('Error fetching dashboard overview:', error);
      throw error;
    }
  },

  // Get performance metrics
  getPerformanceMetrics: async (dateRange) => {
    try {
      const response = await Get(`${BASE_URL}/analytics/performance-metrics`, {
        params: dateRange
      });
      return response;
    } catch (error) {
      console.error('Error fetching performance metrics:', error);
      return {};
    }
  }
};

// Export all APIs
export {
  sequenceAnalyticsAPI as default,
  sequenceManagementAPI,
  realTimeAPI,
  dashboardAPI
};
