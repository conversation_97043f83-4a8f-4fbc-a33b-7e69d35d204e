import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

export class SequenceStepsDto {
  @ApiProperty({
    description: 'Name of the sequence step',
    example: 'Initial Outreach',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Order of the sequence step',
    example: 0,
    default: 0,
  })
  @IsInt()
  @IsOptional()
  order?: number;

  @ApiProperty({
    description: 'Type of the sequence step',
    enum: ['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'NO_RESPONSE_FOLLOW_UP', 'OTHER'],
    default: 'OUTREACH',
  })
  @IsEnum(['OUTREACH', 'FOLLOW_UP', 'REMINDER', 'NO_RESPONSE_FOLLOW_UP', 'OTHER'])
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Medium of the sequence step',
    enum: ['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER'],
    default: 'EMAIL',
  })
  @IsEnum(['EMAIL', 'SMS', 'CALL', 'LINKEDIN', 'WHATSAPP', 'OTHER'])
  @IsNotEmpty()
  medium: string;

  @ApiProperty({
    description: 'ID of the associated email template',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  templateId?: number;

  @ApiProperty({
    description: 'ID of the associated role sequence',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  roleSequenceId?: number;

  @ApiProperty({
    description: 'Hours to wait before triggering follow-up if no response',
    example: 24,
  })
  @IsInt()
  @IsOptional()
  noResponseDelayHours?: number;

  @ApiProperty({
    description: 'Whether this step has follow-up actions for no response',
    example: false,
  })
  @IsBoolean()
  @IsOptional()
  hasNoResponseFollowUp?: boolean;

  @ApiProperty({
    description: 'ID of the parent step for follow-up steps',
    example: 1,
  })
  @IsInt()
  @IsOptional()
  parentStepId?: number;

  @ApiProperty({
    description: 'When to trigger the follow-up step',
    enum: ['IMMEDIATE', 'DELAYED', 'CONDITIONAL'],
    default: 'DELAYED',
  })
  @IsEnum(['IMMEDIATE', 'DELAYED', 'CONDITIONAL'])
  @IsOptional()
  followUpTrigger?: string;

  @ApiProperty({
    description: 'JSON configuration for follow-up conditions',
    example: '{"responseRequired": true, "maxAttempts": 3}',
  })
  @IsString()
  @IsOptional()
  followUpConditions?: string;
}
