import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Roles } from './roles.entity';
import { Repository } from 'typeorm';
import { RoleDto } from './dto/roles.dto';
import { UpdateRoleDto } from './dto/updateRole.dto';
import { RoleLogs } from 'src/role_logs/role_logs.entity';
import {
  RoleLogsAction,
  RoleLogsStatus,
  RoleLogsType,
} from 'src/role_logs/dto/rol_los.enum';
import { RoleHistory } from 'src/role-history/role-history.entity';
import { AddTrialFromWebsiteDto } from './dto/addTrialFromWebsite.dto';
import { People } from 'src/people/people.entity';
import { Company } from 'src/company/company.entity';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Roles)
    private rolesRepository: Repository<Roles>,
    @InjectRepository(RoleLogs)
    private roleLogsRepository: Repository<RoleLogs>,
    @InjectRepository(RoleHistory)
    private roleHistoryRepository: Repository<RoleHistory>,

    @InjectRepository(People)
    private peopleRepository: Repository<People>,

    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
  ) {}

  async createRole(role: RoleDto): Promise<Roles> {
    try {
      // Validation for FIX category
      if (role.category === 'FIX' && (!role.start_date || !role.end_date)) {
        throw new BadRequestException(
          'Start and end date are required for FIX category',
        );
      }

      role.current_status = 'PENDING at RESOURCER';

      const newRole = this.rolesRepository.create(role);
      const savedRole = await this.rolesRepository.save(newRole); // Using `save` to return full entity

      const baseLogData: Partial<RoleLogs> = {
        roleId: savedRole.id,
        details: `Role ${savedRole.title} created`,
        role_number: savedRole.role_number,
        comment: `Role ${savedRole.title} created`,
        userId: role.userId,
      };

      if (role.category === 'FIX') {
        const startDate = new Date(role.start_date);
        const endDate = new Date(role.end_date);
        const logs: RoleLogs[] = [];

        for (
          let d = new Date(startDate);
          d <= endDate;
          d.setDate(d.getDate() + 1)
        ) {
          logs.push(this.buildRoleLog(baseLogData, new Date(d)));
        }

        await this.roleLogsRepository.insert(logs); // Batch insert
      } else {
        const log = this.buildRoleLog(baseLogData, new Date());
        await this.roleLogsRepository.insert(log);
      }

      return savedRole;
    } catch (error) {
      console.error('Create Role Error:', error);
      throw new InternalServerErrorException({
        message: 'Failed to create role',
        error: error.message,
      });
    }
  }

  private buildRoleLog(base: Partial<RoleLogs>, timestamp: Date): RoleLogs {
    const log = new RoleLogs();
    log.roleId = base.roleId;
    log.action = RoleLogsAction.CREATE;
    log.role_date = timestamp;
    log.timestamp = timestamp;
    log.details = base.details;
    log.role_number = base.role_number;
    log.log_status_type = RoleLogsStatus.PENDING;
    log.log_status_at = RoleLogsType.RESOURCER;
    log.start_time = timestamp;
    log.comment = base.comment;
    log.time_spent = '0';
    log.next_stage = `${RoleLogsStatus.PENDING} at ${RoleLogsType.RESOURCER}`;
    log.status = `${RoleLogsStatus.PENDING} at ${RoleLogsType.RESOURCER}`;
    log.userId = base.userId;
    return log;
  }

  async updateRole(id: number, role: UpdateRoleDto): Promise<Roles> {
    try {
      const existingRole = await this.rolesRepository.findOne({
        where: { id },
      });

      if (!existingRole) {
        throw new NotFoundException('Role not found');
      }

      // Check if role category is FIX, then validate dates
      if (role.category === 'FIX') {
        if (!role.start_date || !role.end_date) {
          throw new BadRequestException(
            'Start and end date are required for FIX category',
          );
        }
      }

      // Check if new attachment URL is provided
      console.log(
        typeof existingRole.attachments,
        'sdfsdfdsffsdf',
        existingRole.attachments,
      );
      if (role.attachments) {
        await this.roleHistoryRepository.save({
          roleId: existingRole.id,
          attachment_url: existingRole.attachments,
        });
      }

      // Merge and save updated role
      const updatedRole = this.rolesRepository.merge(existingRole, role);
      const savedRole = await this.rolesRepository.save(updatedRole);

      return savedRole;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to update role',
        error.message,
      );
    }
  }

  async findRoleById(id: number): Promise<Roles> {
    try {
      const role = await this.rolesRepository.findOne({
        where: { id },
        relations: ['roleLogs'],
      });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      return role;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find role',
        error.message,
      );
    }
  }

  async findRoleByTitle(title: string): Promise<Roles> {
    try {
      const role = await this.rolesRepository.findOne({
        where: { title },
      });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      return role;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find role',
        error.message,
      );
    }
  }

  async findRoleByCategory(category: string): Promise<Roles[]> {
    try {
      const roles = await this.rolesRepository.find({
        where: { category },
      });
      if (!roles || roles.length === 0) {
        throw new NotFoundException('No roles found for this category');
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  async findRoleByPriority(priority: string): Promise<Roles[]> {
    try {
      const roles = await this.rolesRepository.find({
        where: { priority },
      });
      if (!roles || roles.length === 0) {
        throw new NotFoundException('No roles found for this priority');
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  async findRoleBySalaryRange(min: string, max: string): Promise<Roles[]> {
    try {
      const roles = await this.rolesRepository.find({
        where: { salary_min: min, salary_max: max },
      });
      if (!roles || roles.length === 0) {
        throw new NotFoundException('No roles found for this salary range');
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  async findRoleByPaymentType(paymentType: string): Promise<Roles[]> {
    try {
      const roles = await this.rolesRepository.find({
        where: { salary_payment_type: paymentType },
      });
      if (!roles || roles.length === 0) {
        throw new NotFoundException('No roles found for this payment type');
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  async findRoleByPostalCode(postalCode: string): Promise<Roles[]> {
    try {
      const roles = await this.rolesRepository.find({
        where: { postal_code: postalCode },
      });
      if (!roles || roles.length === 0) {
        throw new NotFoundException('No roles found for this postal code');
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  async deleteRole(id: number): Promise<void> {
    try {
      const role = await this.rolesRepository.findOne({
        where: { id },
      });
      if (!role) {
        throw new NotFoundException('Role not found');
      }
      await this.rolesRepository.delete({ id });
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to delete role',
        error.message,
      );
    }
  }

  async getRoleServiceStats(
    start_date?: Date,
    end_date?: Date,
    serviceId?: number,
  ): Promise<{ service_name: string; total: number }[]> {
    try {
      console.log('Fetching roles service stats...');

      // Check if there are any services available
      const servicesCount = await this.rolesRepository.count();
      if (servicesCount === 0) {
        return [];
      }

      // Build query dynamically based on optional parameters
      const queryBuilder = this.rolesRepository
        .createQueryBuilder('roles') // ✅ Fix alias
        .leftJoin('roles.service', 'service') // ✅ Reference service through roles
        .select('service.id', 'service_id')
        .addSelect('service.name', 'service_name')
        .addSelect('COUNT(roles.id)', 'total')
        .groupBy('service.id, service.name');

      if (serviceId) {
        queryBuilder.andWhere('service.id = :serviceId', { serviceId });
      }

      if (start_date && end_date) {
        queryBuilder.andWhere(
          'roles.start_date BETWEEN :start_date AND :end_date',
          { start_date, end_date },
        );
      } else if (start_date) {
        queryBuilder.andWhere('roles.start_date >= :start_date', {
          start_date,
        });
      } else if (end_date) {
        queryBuilder.andWhere('roles.start_date <= :end_date', {
          end_date,
        });
      }

      const stats = await queryBuilder.getRawMany();

      return stats.length > 0 ? stats : [];
    } catch (error) {
      console.error('Error retrieving service client stats:', error);
      throw new InternalServerErrorException({
        message: 'Error retrieving service client stats',
        error: error.message,
      });
    }
  }

  async findRoleByService(serviceId: number): Promise<Roles[]> {
    try {
      const roles = await this.rolesRepository.find({
        where: { serviceId },
      });
      if (!roles || roles.length === 0) {
        throw new NotFoundException('No roles found for this service');
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  async findRoleByCategoryAndService(
    category: string,
    serviceId?: number,
  ): Promise<Roles[]> {
    try {
      const whereCondition: any = { category };
      if (serviceId) {
        whereCondition.serviceId = serviceId;
      }

      const roles = await this.rolesRepository.find({
        where: whereCondition,
      });

      if (!roles || roles.length === 0) {
        throw new NotFoundException(
          'No roles found for this category and service',
        );
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  async findRoleByPersonId(personId: string): Promise<Roles[]> {
    try {
      const roles = await this.rolesRepository.find({
        where: { personId },
      });
      if (!roles || roles.length === 0) {
        throw new NotFoundException('No roles found for this person');
      }
      return roles;
    } catch (error) {
      throw new InternalServerErrorException(
        'Failed to find roles',
        error.message,
      );
    }
  }

  // async findAllTrialRoles(
  //   start_date?: Date,
  //   end_date?: Date,
  //   isAdvance?: boolean,
  //   isPrevious?: boolean,
  //   type?: 'cvsourcing' | 'preQualification' | 'direct' | 'trial', // New type filter
  //   bdUserId?: string,
  //   serviceId?: number,
  //   userId?: string,
  //   acmUserId?: string,
  //   roleId?: number,
  //   searchString?: string,
  // ): Promise<{
  //   cvsourcingRoles: Roles[];
  //   preQualificationRoles: Roles[];
  //   directRoles: Roles[];
  //   trialRoles: Roles[];
  // }> {
  //   try {
  //     const queryBuilder = this.rolesRepository
  //       .createQueryBuilder('role')
  //       .leftJoinAndSelect('role.service', 'service')
  //       .leftJoinAndSelect('role.person', 'person')
  //       .leftJoinAndSelect('role.roleLogs', 'roleLogs')
  //       .leftJoinAndSelect('role.focus_points', 'focus_points');

  //     // **Apply Type Filter**
  //     if (type === 'cvsourcing') {
  //       queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 1 });
  //     } else if (type === 'preQualification') {
  //       queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 2 });
  //     } else if (type === 'direct') {
  //       queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 3 });
  //     } else if (type === 'trial') {
  //       queryBuilder.andWhere('role.category = :category', {
  //         category: 'TRIAL',
  //       });
  //     }

  //     if (start_date && end_date && start_date === end_date) {
  //       // Exact match
  //       queryBuilder.andWhere('role.start_date = :start_date::date', { start_date });
  //     } else if (start_date && end_date) {
  //       // Date range
  //       queryBuilder.andWhere(
  //         'role.start_date BETWEEN :start_date::date AND :end_date::date',
  //         { start_date, end_date },
  //       );
  //     } else if (start_date) {
  //       queryBuilder.andWhere('role.start_date >= :start_date::date', { start_date });
  //     } else if (end_date) {
  //       queryBuilder.andWhere('role.start_date <= :end_date::date', { end_date });
  //     }

  //     // if (isAdvance) {
  //     //   queryBuilder.andWhere('role.start_date > :today', { today: new Date() });
  //     // }
  //     // if (isPrevious) {
  //     //   queryBuilder.andWhere('role.start_date < :today', { today: new Date() });
  //     // }

  //     // **Search Filter**
  //     if (
  //       searchString.trim() !== '' &&
  //       searchString !== undefined &&
  //       searchString
  //     ) {
  //       queryBuilder.andWhere(
  //         `
  //         role.title ILIKE :searchString
  //         OR CAST(role.role_number AS TEXT) ILIKE :searchString
  //         OR CAST(role.client_number AS TEXT) ILIKE :searchString
  //       `,
  //         {
  //           searchString: `%${searchString}%`,
  //         },
  //       );
  //     }

  //     // **Additional Filters**
  //     if (bdUserId)
  //       queryBuilder.andWhere('role.bdUserId = :bdUserId', { bdUserId });
  //     if (serviceId)
  //       queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId });
  //     if (userId) queryBuilder.andWhere('role.userId = :userId', { userId });
  //     if (acmUserId)
  //       queryBuilder.andWhere('role.acmUserId = :acmUserId', { acmUserId });
  //     if (roleId) queryBuilder.andWhere('role.id = :roleId', { roleId });

  //     // add user table join
  //     queryBuilder.leftJoinAndSelect('role.user', 'user');
  //     queryBuilder.leftJoinAndSelect('role.acmUser', 'acmUser');
  //     queryBuilder.leftJoinAndSelect('role.bdUser', 'bdUser');
  //     // **Sort by start_date descending**
  //     queryBuilder.orderBy('role.start_date', 'DESC');

  //     // **Fetch roles**
  //     const roles = await queryBuilder.getMany();

  //     if (!roles || roles.length === 0) {
  //       return {
  //         cvsourcingRoles: [],
  //         preQualificationRoles: [],
  //         directRoles: [],
  //         trialRoles: [],
  //       };
  //     }

  //     // **Separate roles into four arrays**
  //     const cvsourcingRoles = roles.filter((role) => role.service?.id === 1);
  //     const preQualificationRoles = roles.filter(
  //       (role) => role.service?.id === 2,
  //     );
  //     const directRoles = roles.filter((role) => role.service?.id === 3);
  //     const trialRoles = roles.filter((role) => role.category === 'TRIAL');

  //     return {
  //       cvsourcingRoles,
  //       preQualificationRoles,
  //       directRoles,
  //       trialRoles,
  //     };
  //   } catch (error) {
  //     console.error('Error retrieving roles:', error);
  //     throw new InternalServerErrorException({
  //       message: 'Error retrieving roles',
  //       error: error.message,
  //     });
  //   }
  // }

  async findAllTrialRoles(
    start_date?: Date,
    end_date?: Date,
    isAdvance?: boolean,
    isPrevious?: boolean,
    type?: 'cvsourcing' | 'preQualification' | 'direct' | 'trial',
    bdUserId?: string,
    serviceId?: number,
    userId?: string,
    acmUserId?: string,
    roleId?: number,
    searchString?: string,
    roleNumber?: string,
    clientNumber?: string,
  ): Promise<{
    cvsourcingRoles: Roles[];
    preQualificationRoles: Roles[];
    directRoles: Roles[];
    trialRoles: Roles[];
  }> {
    try {
      console.log(
        'jksdgfisdbfsdbfnsdf',
        Boolean(roleNumber),
        Boolean(clientNumber),
      );
      const queryBuilder = this.rolesRepository
        .createQueryBuilder('role')
        .leftJoinAndSelect('role.service', 'service')
        .leftJoinAndSelect('role.person', 'person')
        .leftJoinAndSelect('role.roleLogs', 'roleLogs')
        .leftJoinAndSelect('role.focus_points', 'focus_points');

      // **Apply Type Filter**
      if (type === 'cvsourcing') {
        queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 1 });
      } else if (type === 'preQualification') {
        queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 2 });
      } else if (type === 'direct') {
        queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId: 3 });
      } else if (type === 'trial') {
        queryBuilder.andWhere('role.category = :category', {
          category: 'TRIAL',
        });
      }

      // **Normalize and Compare Dates**
      const startStr = start_date?.toString().slice(0, 10);
      const endStr = end_date?.toString().slice(0, 10);

      if (startStr && endStr && startStr === endStr) {
        queryBuilder.andWhere('role.start_date = :start_date', {
          start_date: startStr,
        });
      } else if (startStr && endStr) {
        queryBuilder.andWhere(
          'role.start_date BETWEEN :start_date AND :end_date',
          {
            start_date: startStr,
            end_date: endStr,
          },
        );
      } else if (startStr) {
        queryBuilder.andWhere('role.start_date >= :start_date', {
          start_date: startStr,
        });
      } else if (endStr) {
        queryBuilder.andWhere('role.start_date <= :end_date', {
          end_date: endStr,
        });
      }

      // **Search Filter**
      if (searchString?.trim()) {
        queryBuilder.andWhere(
          `
          role.title ILIKE :searchString
          OR CAST(role.role_number AS TEXT) ILIKE :searchString 
          OR CAST(role.client_number AS TEXT) ILIKE :searchString
        `,
          {
            searchString: `%${searchString}%`,
          },
        );
      }

      // **Additional Filters**
      if (bdUserId)
        queryBuilder.andWhere('role.bdUserId = :bdUserId', { bdUserId });
      if (serviceId)
        queryBuilder.andWhere('role.serviceId = :serviceId', { serviceId });
      if (userId) queryBuilder.andWhere('role.userId = :userId', { userId });
      if (acmUserId)
        queryBuilder.andWhere('role.acmUserId = :acmUserId', { acmUserId });
      if (roleId) queryBuilder.andWhere('role.id = :roleId', { roleId });

      // **Join user-related tables**
      queryBuilder.leftJoinAndSelect('role.user', 'user');
      queryBuilder.leftJoinAndSelect('role.acmUser', 'acmUser');
      queryBuilder.leftJoinAndSelect('role.bdUser', 'bdUser');

      // **Sort by start_date descending**
      queryBuilder.orderBy('role.start_date', 'DESC');

      // **Fetch roles**
      const roles = await queryBuilder.getMany();

      if (!roles || roles.length === 0) {
        return {
          cvsourcingRoles: [],
          preQualificationRoles: [],
          directRoles: [],
          trialRoles: [],
        };
      }

      // **Separate roles into four arrays**
      const cvsourcingRoles = roles.filter((role) => role.service?.id === 1);
      const preQualificationRoles = roles.filter(
        (role) => role.service?.id === 2,
      );
      const directRoles = roles.filter((role) => role.service?.id === 3);
      const trialRoles = roles.filter((role) => role.category === 'TRIAL');

      return {
        cvsourcingRoles,
        preQualificationRoles,
        directRoles,
        trialRoles,
      };
    } catch (error) {
      console.error('Error retrieving roles:', error);
      throw new InternalServerErrorException({
        message: 'Error retrieving roles',
        error: error.message,
      });
    }
  }

  async findCvSourcingRoles(
    isAdvance?: boolean,
    isPrevious?: boolean,
    start_date?: Date,
    end_date?: Date,
  ): Promise<{ category: string; roles: Roles[] }[]> {
    try {
      const queryBuilder = this.rolesRepository
        .createQueryBuilder('role')
        .leftJoinAndSelect('role.service', 'service')
        .leftJoinAndSelect('role.person', 'person')
        .leftJoinAndSelect('role.roleLogs', 'roleLogs')
        .where('role.serviceId = :serviceId', { serviceId: 1 });

      const today = new Date();
      today.setUTCHours(0, 0, 0, 0); // Ensure day-level comparison

      if (isAdvance) {
        queryBuilder.andWhere('roleLogs.role_date > :today', { today });
      }

      if (isPrevious) {
        queryBuilder.andWhere('roleLogs.role_date < :today', { today });
      }

      const normalizeDate = (date: Date): Date => {
        const normalized = new Date(date);
        normalized.setUTCHours(0, 0, 0, 0);
        return normalized;
      };

      // Normalize dates to prevent timezone edge cases
      const start = start_date ? normalizeDate(start_date) : undefined;
      const end = end_date ? normalizeDate(end_date) : undefined;

      if (start && end && !isAdvance && !isPrevious) {
        queryBuilder.andWhere('roleLogs.role_date BETWEEN :start AND :end', {
          start,
          end,
        });
      } else if (start && !isAdvance && !isPrevious) {
        queryBuilder.andWhere('roleLogs.role_date >= :start', { start });
      } else if (end && !isAdvance && !isPrevious) {
        queryBuilder.andWhere('roleLogs.role_date <= :end', { end });
      }

      queryBuilder.leftJoinAndSelect('role.user', 'user');
      queryBuilder.leftJoinAndSelect('role.acmUser', 'acmUser');
      queryBuilder.leftJoinAndSelect('role.bdUser', 'bdUser');

      const roles = await queryBuilder.getMany();

      if (!roles.length) return [];

      const groupedRoles = roles.reduce(
        (acc, role) => {
          if (!acc[role.category]) {
            acc[role.category] = [];
          }
          acc[role.category].push(role);
          return acc;
        },
        {} as Record<string, Roles[]>,
      );

      return Object.entries(groupedRoles).map(([category, roles]) => ({
        category,
        roles,
      }));
    } catch (error) {
      console.error('Error retrieving CVSourcing roles:', error);
      throw new InternalServerErrorException({
        message: 'Error retrieving CVSourcing roles',
        error: error.message,
      });
    }
  }

  async find360_PreQualificationRoles(
    isAdvance?: boolean,
    isPrevious?: boolean,
    start_date?: Date,
    end_date?: Date,
    serviceId?: number,
  ): Promise<{ client_number: string; roles: Roles[] }[]> {
    try {
      const queryBuilder = this.rolesRepository
        .createQueryBuilder('role')
        .leftJoinAndSelect('role.service', 'service')
        .leftJoinAndSelect('role.person', 'person')
        .leftJoinAndSelect('role.roleLogs', 'roleLogs')
        .where('role.serviceId = :serviceId', { serviceId });

      if (isAdvance) {
        queryBuilder.andWhere('roleLogs.role_date > :today', {
          today: new Date(),
        });
      }
      if (isPrevious) {
        queryBuilder.andWhere('roleLogs.role_date < :today', {
          today: new Date(),
        });
      }
      if (start_date && end_date && !isAdvance && !isPrevious) {
        queryBuilder.andWhere(
          'roleLogs.role_date BETWEEN :start_date AND :end_date',
          {
            start_date,
            end_date,
          },
        );
      } else if (start_date && !isAdvance && !isPrevious) {
        queryBuilder.andWhere('roleLogs.role_date >= :start_date', {
          start_date,
        });
      } else if (end_date && !isAdvance && !isPrevious) {
        queryBuilder.andWhere('roleLogs.role_date <= :end_date', {
          end_date,
        });
      }

      queryBuilder.leftJoinAndSelect('role.user', 'user');
      queryBuilder.leftJoinAndSelect('role.acmUser', 'acmUser');
      queryBuilder.leftJoinAndSelect('role.bdUser', 'bdUser');

      const roles = await queryBuilder.getMany();
      if (!roles || roles.length === 0) {
        return []; // No roles found
      }

      // **Group roles by client_number**
      const groupedRoles = roles.reduce(
        (acc, role) => {
          const clientNumber = role.client_number || 'Unknown'; // Handle undefined client numbers
          if (!acc[clientNumber]) {
            acc[clientNumber] = [];
          }
          acc[clientNumber].push(role);
          return acc;
        },
        {} as Record<string, Roles[]>,
      );

      // **Return grouped data in the correct format**
      return Object.entries(groupedRoles).map(([client_number, roles]) => ({
        client_number,
        roles,
      }));
    } catch (error) {
      console.error('Error retrieving PreQualification roles:', error);
      throw new InternalServerErrorException({
        message: 'Error retrieving PreQualification roles',
        error: error.message,
      });
    }
  }

  async addTrialFromWebsite(trialRole: AddTrialFromWebsiteDto): Promise<Roles> {
    try {
      // 1. Extract domain from website
      const website = trialRole.companyWebsite?.trim();
      if (!website)
        throw new BadRequestException('Company website is required');

      const domainMatch = website.match(/(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
      const domain = domainMatch
        ? domainMatch[1].toLowerCase()
        : website.toLowerCase();
      if (!domain) throw new BadRequestException('Invalid company website');

      // 2. Check or create company
      let company = await this.companyRepository.findOne({
        where: { website: domain },
      });
      if (!company) {
        company = this.companyRepository.create({
          name: trialRole.companyName,
          website: domain,
          company_country: trialRole.country,
          industry: trialRole.companyIndustry,
          company_phone: trialRole.companyPhone,
        });
        company = await this.companyRepository.save(company);
      }

      // 3. Check or create person by business email
      const personEmailRepo =
        this.peopleRepository.manager.getRepository('PersonEmail');
      let personEmail = await personEmailRepo.findOne({
        where: { email: trialRole.businessEmail },
      });
      let person: People | null = null;

      if (personEmail?.personId) {
        person = await this.peopleRepository.findOne({
          where: { id: personEmail.personId },
        });
      }

      if (!person) {
        // No person found, create new one
        person = this.peopleRepository.create({
          first_name: trialRole.fullName.split(' ')[0],
          last_name: trialRole.fullName.split(' ').slice(1).join(' '),
          current_title: trialRole.jobTitle,
          companyId: company.id,
        });
        person = await this.peopleRepository.save(person);

        // Check if email already exists before saving
        const existingEmail = await personEmailRepo.findOne({
          where: { email: trialRole.businessEmail },
        });
        if (!existingEmail) {
          await personEmailRepo.save({
            email: trialRole.businessEmail,
            email_type: 'BUSINESS',
            personId: person.id,
          });
        }
      } else {
        // Person exists but maybe with different company
        if (person.companyId !== company.id) {
          const existingPersonAtCompany = await this.peopleRepository.findOne({
            where: {
              first_name: trialRole.fullName.split(' ')[0],
              last_name: trialRole.fullName.split(' ').slice(1).join(' '),
              companyId: company.id,
            },
          });

          if (existingPersonAtCompany) {
            person = existingPersonAtCompany;
          } else {
            person = this.peopleRepository.create({
              first_name: trialRole.fullName.split(' ')[0],
              last_name: trialRole.fullName.split(' ').slice(1).join(' '),
              current_title: trialRole.jobTitle,
              companyId: company.id,
            });
            person = await this.peopleRepository.save(person);
          }

          // Check if email is already linked to this person
          const alreadyLinked = await personEmailRepo.findOne({
            where: { email: trialRole.businessEmail, personId: person.id },
          });

          if (!alreadyLinked) {
            await personEmailRepo.save({
              email: trialRole.businessEmail,
              email_type: 'BUSINESS',
              personId: person.id,
            });
          }
        }
      }

      // 4. Save Role
      const role = this.rolesRepository.create({
        title: trialRole.roleMainTitle,
        relevant_titles: trialRole.roleRelevantTitles,
        locations: trialRole.location,
        postal_code: trialRole.postalCode,
        radius_miles: trialRole.radiusMiles,
        willing_to_relocate: trialRole.willingToRelocate,
        open_to_work: trialRole.openToWork,
        salary_min: trialRole.salaryMin,
        salary_max: trialRole.salaryMax,
        essential_qualifications: trialRole.essentialQualifications,
        essential_requirements: trialRole.essentialRequirements,
        desireable_or_preferred_requirements:
          trialRole.desireableOrPreferredRequirements,
        companies_of_interest: trialRole.companiesOfInterest,
        major_reason_for_rejected_cvs: trialRole.majorReasonForRejectedCVs
          ? [trialRole.majorReasonForRejectedCVs]
          : [],
        who_do_we_want: trialRole.whoDoWeWant,
        who_do_we_not_want: trialRole.whoDoWeNotWant,
        employement_type: trialRole.contractPermanent,
        role_industry: trialRole.roleIndustry,
        attachments: trialRole.attachments,
        source: 'WEBSITE',
      });

      return await this.rolesRepository.save(role);
    } catch (error) {
      console.error('Error adding trial role:', error);
      throw new InternalServerErrorException({
        message: 'Failed to add trial role',
        error: error?.message || error,
      });
    }
  }

  async rolesDataForBdDashboard() {
    const statusColorMap = {
      Total: '#5d7cef',
      'Pending At Resourcer': '#2caffe',
      Paused: '#00e272',
      Issue: '#fe6a35',
      'Pending at ACM': '#fcb92c',
      Done: '#41cc83',
    };

    try {
      const today = new Date();
      const lastMonday = new Date(today);
      lastMonday.setDate(today.getDate() - ((today.getDay() + 6) % 7)); // This week Monday
      lastMonday.setDate(lastMonday.getDate() - 7); // Last week Monday

      const lastSunday = new Date(lastMonday);
      lastSunday.setDate(lastMonday.getDate() + 6); // Last week Sunday

      // 2. Fetch roles created last week
      const roles = await this.rolesRepository
        .createQueryBuilder('role')
        .where('role.created_at BETWEEN :start AND :end', {
          start: lastMonday.toISOString().slice(0, 10),
          end: lastSunday.toISOString().slice(0, 10),
        })
        .andWhere('role.category = :category', { category: 'TRIAL' })
        .getMany();

      // 3. Group roles by day of the week (Monday, Tuesday, ...)
      const daysOfWeek = [
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday',
        'Sunday',
      ];
      const groupedByDay = {};

      // Initialize group
      for (let i = 0; i < 7; i++) {
        groupedByDay[daysOfWeek[i]] = [];
      }

      roles.forEach((role) => {
        const roleDate = new Date(role.created_at);
        // Get day index: 0=Monday, ..., 6=Sunday
        const dayIndex = (roleDate.getDay() + 6) % 7;
        const dayName = daysOfWeek[dayIndex];
        groupedByDay[dayName].push(role);
      });

      // 4. Build data structure for each day
      const result = daysOfWeek.map((day) => {
        const rolesForDay = groupedByDay[day];
        const statusCounts = {
          'Pending At Resourcer': 0,
          Paused: 0,
          Issue: 0,
          'Pending at ACM': 0,
          Done: 0,
        };

        // Count statuses
        rolesForDay.forEach((role) => {
          const status = role.current_status;
          if (statusCounts.hasOwnProperty(status)) {
            statusCounts[status]++;
          }
        });

        // Calculate total
        const total = rolesForDay.length;

        // Compose data array in requested order
        const data = [
          { name: 'Total', count: total, color: statusColorMap['Total'] },
          {
            name: 'Pending At Resourcer',
            count: statusCounts['Pending At Resourcer'],
            color: statusColorMap['Pending At Resourcer'],
          },
          {
            name: 'Paused',
            count: statusCounts['Paused'],
            color: statusColorMap['Paused'],
          },
          {
            name: 'Issue',
            count: statusCounts['Issue'],
            color: statusColorMap['Issue'],
          },
          {
            name: 'Pending at ACM',
            count: statusCounts['Pending at ACM'],
            color: statusColorMap['Pending at ACM'],
          },
          {
            name: 'Done',
            count: statusCounts['Done'],
            color: statusColorMap['Done'],
          },
        ];

        return { day, data };
      });

      return result;
    } catch (error) {
      throw new InternalServerErrorException({
        message: 'Failed to add trial role',
        error: error?.message || error,
      });
    }
  }
}
