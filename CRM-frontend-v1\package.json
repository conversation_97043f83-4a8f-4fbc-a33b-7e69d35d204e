{"name": "crm-frontend", "version": "1.3.0", "private": true, "homepage": "https://mantisdashboard.io/free", "author": {"name": "CodedThemes", "email": "<EMAIL>", "url": "https://codedthemes.com/"}, "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\""}, "dependencies": {"@ant-design/colors": "^7.0.2", "@ant-design/icons": "^5.3.1", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.0.17", "@fontsource/poppins": "^5.0.12", "@fontsource/public-sans": "^5.0.17", "@fontsource/roboto": "^5.0.12", "@fortawesome/fontawesome-free": "^6.7.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@iconify/react": "^5.2.0", "@mdxeditor/editor": "^3.23.0", "@mui/base": "^5.0.0-beta.38", "@mui/icons-material": "^7.0.2", "@mui/lab": "^5.0.0-alpha.167", "@mui/material": "^5.15.12", "@mui/system": "^5.15.12", "@mui/x-date-pickers": "^7.27.0", "@progress/kendo-drawing": "^1.21.2", "@progress/kendo-file-saver": "^1.1.2", "@progress/kendo-licensing": "^1.6.0", "@progress/kendo-react-common": "^10.1.0", "@progress/kendo-react-pdf": "8.5.0", "@reduxjs/toolkit": "^2.5.1", "@svgr/webpack": "^8.1.0", "@tiptap/core": "^2.22.3", "@tiptap/extension-code-block": "^2.22.3", "@tiptap/extension-code-block-lowlight": "^2.22.3", "@tiptap/extension-color": "^2.23.1", "@tiptap/extension-dropcursor": "^2.22.3", "@tiptap/extension-gapcursor": "^2.22.3", "@tiptap/extension-highlight": "^2.22.3", "@tiptap/extension-horizontal-rule": "^2.22.3", "@tiptap/extension-image": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-list-item": "^2.22.3", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-task-item": "^2.22.3", "@tiptap/extension-task-list": "^2.22.3", "@tiptap/extension-text-align": "^2.22.3", "@tiptap/extension-text-style": "^2.23.1", "@tiptap/extension-typography": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.23.1", "@tiptap/starter-kit": "^2.23.1", "@twilio/voice-sdk": "^2.12.4", "@vitejs/plugin-react": "^4.2.1", "antd": "^5.23.3", "antd-img-crop": "^4.24.0", "apexcharts": "^4.7.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "country-state-city": "^3.2.1", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "formik": "^2.4.5", "framer-motion": "^11.0.8", "highcharts": "^12.2.0", "highcharts-react-official": "^3.2.1", "highlight.js": "^11.11.1", "html2canvas": "^1.4.1", "iconify-icon": "^2.3.0", "jspdf": "^3.0.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.12.6", "lodash": "^4.17.21", "lowlight": "^3.3.0", "marked": "^15.0.7", "moment": "^2.30.1", "process": "^0.11.10", "prop-types": "^15.8.1", "react": "18", "react-alice-carousel": "^2.9.1", "react-apexcharts": "^1.7.0", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^5.1.0", "react-copy-to-clipboard": "^5.1.0", "react-datepicker": "^8.1.0", "react-device-detect": "^2.2.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-doc-viewer": "^0.1.14", "react-dom": "18", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.5", "react-error-boundary": "^5.0.0", "react-helmet-async": "^2.0.5", "react-markdown": "^10.1.0", "react-multi-carousel": "^2.8.5", "react-number-format": "^5.3.3", "react-phone-number-input": "^3.4.12", "react-quill": "^2.0.0", "react-quill-new": "^3.4.6", "react-redux": "^9.2.0", "react-router": "^6.22.3", "react-router-dom": "^6.22.3", "react-select": "^5.10.1", "react-verification-input": "^4.2.2", "react-xarrows": "^2.0.2", "reactflow": "^11.11.4", "redux-persist": "^6.0.0", "sequential-workflow-designer": "^0.29.2", "sequential-workflow-designer-react": "^0.29.2", "simplebar-react": "^3.2.4", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.15", "swr": "^2.2.5", "turndown": "^7.2.0", "util": "^0.12.5", "uuid": "^11.1.0", "vite": "^5.2.10", "vite-jsconfig-paths": "^2.0.1", "web-vitals": "^3.5.2", "xlsx": "^0.18.5", "yup": "^1.4.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.23.10", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "react-error-overlay": "6.0.11"}}