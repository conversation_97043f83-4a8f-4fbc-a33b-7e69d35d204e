import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { UsersModule } from './users/users.module';
import { LoggerMiddleware } from './logger.middleware';
import { EmailModule } from './email/email.module';
import { CloudinaryModule } from './cloudinary/cloudinary.module';
import { StripeModule } from './stripe/stripe.module';
import { ServiceModule } from './service/service.module';
import { S3bucketModule } from './s3bucket/s3bucket.module';
import { JobsModule } from './jobs/jobs.module';
import { CountryModule } from './country/country.module';
import { SectorModule } from './sector/sector.module';
import { PeopleModule } from './people/people.module';
import { CompanyModule } from './company/company.module';
import { PreferencesModule } from './preferences/preferences.module';
import { JobAlertsModule } from './job-alerts/job-alerts.module';
import { ResumeTemplatesModule } from './resume-templates/resume-templates.module';
import { ResumeModule } from './resume/resume.module';
import { EmailsModule } from './emails/emails.module';
import { PhoneModule } from './phone/phone.module';
import { SkillsModule } from './skills/skills.module';
import { ExperienceModule } from './experience/experience.module';
import { QualificationsModule } from './qualifications/qualifications.module';
import { MailBoxModule } from './mail-box/mail-box.module';
import { RolesModule } from './roles/roles.module';
import { InvoicesModule } from './invoices/invoices.module';
import { CalendarModule } from './calendar/calendar.module';
import { EmailTemplatesModule } from './email-templates/email-templates.module';
import { FocusPointModule } from './focus-point/focus-point.module';
import { RoleLogsModule } from './role_logs/role_logs.module';
import { SequenceModule } from './sequence/sequence.module';
import { SequenceStepsModule } from './sequence-steps/sequence-steps.module';
import { JobApplicationsModule } from './job_applications/job_applications.module';
import { LanguagesModule } from './languages/languages.module';
import { ContactUsModule } from './contact-us/contact-us.module';
import { RoleCandidatesModule } from './role_candidates/role_candidates.module';
import { VoiceModule } from './voice/voice.module';
import { TwillioModule } from './twillio/twillio.module';
import { VolunteerModule } from './volunteer/volunteer.module';
import { ProjectsModule } from './projects/projects.module';
import { PeopleAssignmentsModule } from './people-assignments/people-assignments.module';
import { LeadsModule } from './leads/leads.module';
import { ScrapperModule } from './scrapper/scrapper.module';
import { CompanyScrapperControlModule } from './company_scrapper_control/company_scrapper_control.module';
import { MessagesModule } from './messages/messages.module';
import { CandidateSequenceStatusModule } from './candidate-sequence-status/candidate-sequence-status.module';
import { QueueModule } from './queue/queue.module';
import { WebhooksModule } from './webhooks/webhooks.module';
import { FileManagerModule } from './file-manager/file-manager.module';
import { WebsiteManualRequestsModule } from './website_manual_requests/website_manual_requests.module';
import { CompanyScrapperModule } from './company_scrapper/company_scrapper.module';
import { CandidatesModule } from './candidates/candidates.module';
import { CandidateApplicationModule } from './candidate-application/candidate-application.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    ScheduleModule.forRoot(),
    TypeOrmModule.forRoot({
      type: process.env.DB_TYPE as any,
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT, 10) || 5432,
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      // ssl: {
      //   rejectUnauthorized: false,
      // },
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: true,
      autoLoadEntities: true,
      // // synch database schema with entities
      // dropSchema: true,
      // logging: true,
    }),
    UsersModule,
    CloudinaryModule,
    EmailModule,
    StripeModule,
    ServiceModule,
    S3bucketModule,
    JobsModule,
    CountryModule,
    SectorModule,
    PeopleModule,
    CompanyModule,
    PreferencesModule,
    JobAlertsModule,
    ResumeTemplatesModule,
    ResumeModule,
    EmailsModule,
    PhoneModule,
    SkillsModule,
    ExperienceModule,
    QualificationsModule,
    MailBoxModule,
    RolesModule,
    InvoicesModule,
    CalendarModule,
    EmailTemplatesModule,
    FocusPointModule,
    RoleLogsModule,
    SequenceModule,
    SequenceStepsModule,
    JobApplicationsModule,
    LanguagesModule,
    ContactUsModule,
    RoleCandidatesModule,
    VoiceModule,
    TwillioModule,
    VolunteerModule,
    ProjectsModule,
    PeopleAssignmentsModule,
    LeadsModule,
    ScrapperModule,
    CompanyScrapperControlModule,
    MessagesModule,
    CandidateSequenceStatusModule,
    QueueModule,
    WebhooksModule,
    FileManagerModule,
    WebsiteManualRequestsModule,
    CompanyScrapperModule,
    CandidatesModule,
    CandidateApplicationModule,
  ],
  controllers: [AppController],
  providers: [AppService,],
})
export class AppModule {
  configure(consumer: import('@nestjs/common').MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
