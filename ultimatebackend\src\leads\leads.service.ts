import { PersonType } from './../people/dto/people.enums';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Company } from 'src/company/company.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import {
  AssignmentStatus,
  PeopleAssignment,
} from 'src/people-assignments/entities/people-assignment.entity';
import { People } from 'src/people/people.entity';
import { Users } from 'src/users/users.entity';
import {
  Between,
  ILike,
  In,
  IsNull,
  LessThanOrEqual,
  MoreThanOrEqual,
  Not,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import {
  GetCountryAndSectorWiseLeadsByRegionDto,
  GetCountryAndSectorWiseLeadsStatsDto,
  GetCountryAndSectorWisePersonsDto,
  GetDetailLeadReportsDto,
  GetMarketingEmailsDto,
  GetPersonWiseLeadsStatsDto,
  GetRegionWiseLeadsContactStatsDto,
  GetScrapperReportingDto,
  GetTeamAssignedPersonsStatsDto,
  GetUserWorkReportDto,
} from './dto/leadsQuery.dto';
import { Country } from 'src/country/country.entity';
import { Sector } from 'src/sector/sector.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { PersonEmailType } from 'src/emails/dto/person-email.dto';
import { MailBox } from 'src/mail-box/mailBox.entity';
import { ScrapperStats } from 'src/scrapper/scrapperStats.entity';
import { CompanyScrapper } from 'src/company_scrapper/entities/company_scrapper.entity';

@Injectable()
export class LeadsService {
  constructor(
    @InjectRepository(People)
    private peopleRepository: Repository<People>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    @InjectRepository(PeopleAssignment)
    private peopleAssignmentRepository: Repository<PeopleAssignment>,
    @InjectRepository(Users)
    private usersRepository: Repository<Users>,
    @InjectRepository(PersonEmail)
    private personEmailRepository: Repository<PersonEmail>,
    @InjectRepository(Country)
    private countryRepository: Repository<Country>,
    @InjectRepository(Sector)
    private sectorRepository: Repository<Sector>,
    @InjectRepository(Jobs)
    private jobsRepository: Repository<Jobs>,
    @InjectRepository(MailBox)
    private mailBoxRepository: Repository<MailBox>,
    @InjectRepository(ScrapperStats)
    private scrapperStatsRepository: Repository<ScrapperStats>,
    @InjectRepository(CompanyScrapper)
    private companyScrapperRepository: Repository<CompanyScrapper>,
  ) {}

  async getMarketingEmailsWithPersonName(queryParam: GetMarketingEmailsDto) {
    const { startDate, endDate, searchString, sector_id, page, pageSize } =
      queryParam;

    const safeStartDate =
      typeof startDate === 'string' ? startDate.trim() : undefined;
    const safeEndDate =
      typeof endDate === 'string' ? endDate.trim() : undefined;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    const pageInt = parseInt(page) || 0;
    const pageSizeInt = parseInt(pageSize) || 10;
    const skip = pageInt * pageSizeInt;

    const query = this.personEmailRepository
      .createQueryBuilder('email')
      .leftJoinAndSelect('email.person', 'person')
      .where('email.is_verified = :isVerified', { isVerified: true })
      .andWhere('email.is_unsubscribed = :isUnsubscribed', {
        isUnsubscribed: false,
      });

    if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
      query.andWhere('email.created_at BETWEEN :start AND :end', {
        start: `${safeStartDate}T00:00:00.000Z`,
        end: `${safeEndDate}T23:59:59.999Z`,
      });
    } else if (isValidDate(safeStartDate)) {
      query.andWhere('email.created_at >= :start', {
        start: `${safeStartDate}T00:00:00.000Z`,
      });
    } else if (isValidDate(safeEndDate)) {
      query.andWhere('email.created_at <= :end', {
        end: `${safeEndDate}T23:59:59.999Z`,
      });
    }

    if (sector_id && !isNaN(Number(sector_id))) {
      query.andWhere('person.sectorId = :sectorId', {
        sectorId: Number(sector_id),
      });
    } else {
      query.andWhere('person.sectorId IS NOT NULL');
    }

    if (searchString) {
      query.andWhere('person.first_name ILIKE :search', {
        search: `%${searchString}%`,
      });
    }

    query.orderBy('email.created_at', 'DESC');
    query.skip(skip).take(pageSizeInt);

    const [emails, total] = await query.getManyAndCount();

    return {
      total,
      emails,
      page: pageInt,
      pageSize: pageSizeInt,
    };
  }

  async getCountryAndSectorWiseLeadsStats(
    queryParam: GetCountryAndSectorWiseLeadsStatsDto,
  ) {
    const { country_id, sector_id, from_date, to_date, user_id, industry } =
      queryParam;

    const safeFromDate =
      typeof from_date === 'string' ? from_date.trim() : undefined;
    const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    const whereQuery = {} as any;

    const allPersons = await this.peopleRepository.count();

    // const personIds = allPersons[0].map((person) => person.id);
    const totalPersons = allPersons;

    if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
      whereQuery.created_at = Between(safeFromDate, safeToDate);
    } else if (isValidDate(safeFromDate)) {
      whereQuery.created_at = MoreThanOrEqual(safeFromDate);
    } else if (isValidDate(safeToDate)) {
      whereQuery.created_at = LessThanOrEqual(safeToDate);
    }

    if (country_id && !isNaN(Number(country_id))) {
      whereQuery['person.countryId'] = parseInt(country_id);
    }

    if (sector_id && !isNaN(Number(sector_id))) {
      whereQuery['person.sectorId'] = parseInt(sector_id);
    }

    if (user_id) {
      whereQuery.leadUserId = user_id;
    }

    if (industry) {
      whereQuery['person.industry'] = industry;
    }

    try {
      const withContactInfo = await this.peopleAssignmentRepository.count({
        where: {
          // personId: In(personIds),
          is_business_email_added: true,
          ...whereQuery,
        },
      });

      const withoutContactInfo = await this.peopleAssignmentRepository.count({
        where: {
          // personId: In(personIds),
          is_business_email_added: false,
          ...whereQuery,
        },
      });

      const verifiedLeads = await this.peopleAssignmentRepository.count({
        where: {
          // personId: In(personIds),
          is_working_completed: true,
          ...whereQuery,
        },
      });

      const bounceLeads = await this.peopleAssignmentRepository.count({
        where: {
          // personId: In(personIds),
          is_replacement_needed: true,
          is_bounce_back: true,
          ...whereQuery,
        },
      });

      const emailNotFoundLeads = await this.peopleAssignmentRepository.count({
        where: {
          // personId: In(personIds),
          is_email_not_found: true,
          ...whereQuery,
        },
      });

      const notWorked = await this.peopleAssignmentRepository.count({
        where: {
          // personId: In(personIds),
          leadUser: {
            id: Not(null),
          },
          is_business_email_added: false,
          ...whereQuery,
        },
      });

      // const notAssigned = await this.peopleAssignmentRepository.count({
      //   where: {
      //     personId: Not(In(personIds)),
      //     ...whereQuery,
      //   },
      // });

      const totalAssigned = await this.peopleAssignmentRepository.count({
        where: {
          ...whereQuery,
        },
      });

      const notAssigned = totalPersons - totalAssigned;

      return {
        totalPersons,
        withContactInfo,
        withoutContactInfo,
        verifiedLeads,
        bounceLeads,
        emailNotFoundLeads,
        notWorked,
        notAssigned,
      };
    } catch (error) {
      console.log('Error in getCountryAndSectorWiseLeadsStats:', error);
      throw new InternalServerErrorException(
        'Error in getCountryAndSectorWiseLeadsStats',
      );
    }
  }

  async getCountryAndSectorWisePersons(
    queryParam: GetCountryAndSectorWisePersonsDto,
  ) {
    const {
      country_id,
      sector_id,
      from_date,
      to_date,
      user_id,
      industry,
      page,
      size,
      selectedFilter,
    } = queryParam;

    const pageInt = parseInt(page);
    const pageSizeInt = parseInt(size);

    const limit = pageSizeInt;
    const skip = pageInt * limit;

    const safeFromDate =
      typeof from_date === 'string' ? from_date.trim() : undefined;
    const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    let whereCondition = {} as any;

    if (selectedFilter === 'with') {
      whereCondition.is_business_email_added = true;
    }
    if (selectedFilter === 'without') {
      whereCondition.is_business_email_added = false;
    }
    if (selectedFilter === 'verified') {
      whereCondition.is_working_completed = true;
    }
    if (selectedFilter === 'bounced') {
      whereCondition.is_replacement_needed = true;
    }
    if (selectedFilter === 'checked') {
      whereCondition.is_business_email_added = true;
      whereCondition = [
        { is_working_completed: true },
        { is_replacement_needed: true },
      ];
    }
    if (selectedFilter === 'unchecked') {
      whereCondition.is_business_email_added = true;
      whereCondition = [
        { is_working_completed: false },
        { is_replacement_needed: false },
      ];
    }

    if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
      whereCondition.created_at = Between(safeFromDate, safeToDate);
    } else if (isValidDate(safeFromDate)) {
      whereCondition.created_at = MoreThanOrEqual(safeFromDate);
    } else if (isValidDate(safeToDate)) {
      whereCondition.created_at = LessThanOrEqual(safeToDate);
    }

    if (country_id && !isNaN(Number(country_id))) {
      whereCondition['person.countryId'] = parseInt(country_id);
    }

    if (sector_id && !isNaN(Number(sector_id))) {
      whereCondition['person.sectorId'] = parseInt(sector_id);
    }

    if (user_id) {
      whereCondition.leadUserId = user_id;
    }

    if (industry) {
      whereCondition['person.industry'] = industry;
    }

    const [persons, total] = await this.peopleAssignmentRepository.findAndCount(
      {
        where: {
          ...whereCondition,
        },
        relations: [
          'person',
          'person.company',
          'person.country',
          'person.sector',
          'person.emails',
          'leadUser',
        ],
        skip: skip,
        take: limit,
        order: {
          created_at: 'DESC',
        },
      },
    );

    const processedPersons = persons.map((assignment) => {
      const businessEmails =
        assignment.person?.emails?.filter(
          (email) => email.email_type === 'BUSINESS',
        ) || [];
      const personalEmails =
        assignment.person?.emails?.filter(
          (email) => email.email_type === 'PERSONAL',
        ) || [];

      const emailNotFoundUsers =
        assignment.is_email_not_found === true ? [assignment.person] : [];
      const replacementNotFoundUsers =
        assignment.person?.emails?.filter(
          (email) => email.isReplacedBy != null,
        ) || [];

      return {
        id: assignment.person?.id,
        profile_url: assignment.person?.profile_url,
        full_name:
          `${assignment.person?.first_name || ''} ${assignment.person?.last_name || ''}`.trim(),
        first_name: assignment.person?.first_name,
        last_name: assignment.person?.last_name,
        current_title: assignment.person?.current_title,
        avator: assignment.person?.profile_img,
        headline: assignment.person?.headline,
        industry: assignment.person?.industry,
        SR_specied_industry: assignment.person?.industry,
        is_hiring_person: assignment.is_hiring_person,
        summary: assignment.person?.summary,
        date_of_birth: null,
        is_replacement_needed: assignment.is_replacement_needed,
        is_email_info_added: assignment.is_email_info_added,
        createdAt: assignment.created_at,
        updatedAt: assignment.updated_at,
        company_id: assignment.person?.companyId,
        user_id: assignment.person?.userId,
        sector_id: assignment.person?.sectorId,
        is_email_not_found: assignment.is_email_not_found,
        is_replacement_not_found: assignment.is_replacement_not_found,
        company: assignment.person?.company,
        is_business_email_added: assignment.is_business_email_added,
        country: assignment.person?.country,
        sector: assignment.person?.sector,
        user_assigned: assignment.leadUser,
        businessEmails: businessEmails.map((e) => ({
          id: e.id,
          email_id: e.email,
          user_id: e.isAddedBy,
          is_default_email: e.is_default,
          type: e.email_type,
        })),
        personalEmails: personalEmails.map((e) => ({
          id: e.id,
          email_id: e.email,
          user_id: e.isAddedBy,
          is_default_email: e.is_default,
          type: e.email_type,
        })),
        email_not_found_claim_users: emailNotFoundUsers,
        replacement_not_found_claim_users: replacementNotFoundUsers,
      };
    });

    return {
      data: processedPersons,
      total: total,
      page: pageInt,
      pageSize: pageSizeInt,
      totalPages: Math.ceil(total / pageSizeInt),
    };
  }

  async getTeamAssignedPersonsStats(
    queryParam: GetTeamAssignedPersonsStatsDto,
  ) {
    const { user_id, from_date, to_date, country_id, sector_id } = queryParam;

    const safeFromDate =
      typeof from_date === 'string' ? from_date.trim() : undefined;
    const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    const whereCondition = {} as any;

    if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
      whereCondition.created_at = Between(safeFromDate, safeToDate);
    } else if (isValidDate(safeFromDate)) {
      whereCondition.created_at = MoreThanOrEqual(safeFromDate);
    } else if (isValidDate(safeToDate)) {
      whereCondition.created_at = LessThanOrEqual(safeToDate);
    }

    if (country_id && !isNaN(Number(country_id))) {
      whereCondition['person.countryId'] = parseInt(country_id);
    }

    if (sector_id && !isNaN(Number(sector_id))) {
      whereCondition['person.sectorId'] = parseInt(sector_id);
    }

    if (user_id) {
      whereCondition['leadUserId'] = user_id;
    }

    const allPersons = await this.peopleRepository.findAndCount({
      select: ['id'],
    });

    const personIds = allPersons[0].map((person) => person.id);
    const totalPersons = allPersons[1];

    const withEmailsCount = await this.peopleAssignmentRepository.count({
      where: {
        ...whereCondition,
        is_business_email_added: true,
      },
    });

    const checkedEmailsCount = await this.peopleAssignmentRepository.count({
      where: [
        {
          ...whereCondition,
          is_business_email_added: true,
          is_email_not_found: true,
        },
        {
          ...whereCondition,
          is_business_email_added: true,
          is_replacement_not_found: true,
        },
      ],
    });

    const unCheckedEmailsCount = await this.peopleAssignmentRepository.count({
      where: [
        {
          ...whereCondition,
          is_business_email_added: true,
          is_email_not_found: true,
        },
        {
          ...whereCondition,
          is_business_email_added: true,
          is_replacement_not_found: true,
        },
      ],
    });

    const verifiedEmailsCount = await this.peopleAssignmentRepository.count({
      where: {
        ...whereCondition,
        is_business_email_added: true,
        is_working_completed: true,
      },
    });

    const bouncedEmailsCount = await this.peopleAssignmentRepository.count({
      where: {
        ...whereCondition,
        is_business_email_added: true,
        is_replacement_needed: true,
      },
    });

    const withoutEmailsCount = await this.peopleAssignmentRepository.count({
      where: {
        ...whereCondition,
        is_business_email_added: false,
      },
    });

    return {
      totalPersons,
      withEmailsCount,
      checkedEmailsCount,
      unCheckedEmailsCount,
      verifiedEmailsCount,
      bouncedEmailsCount,
      withoutEmailsCount,
    };
  }

  async getUserWorkReportV3(queryParam: GetUserWorkReportDto) {
    const { user_id, from_date, to_date, country_id, sector_id } = queryParam;

    const safeFromDate =
      typeof from_date === 'string' ? from_date.trim() : undefined;
    const safeToDate = typeof to_date === 'string' ? to_date.trim() : undefined;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    const whereCondition = {} as any;

    if (isValidDate(safeFromDate) && isValidDate(safeToDate)) {
      whereCondition.created_at = Between(safeFromDate, safeToDate);
    } else if (isValidDate(safeFromDate)) {
      whereCondition.created_at = MoreThanOrEqual(safeFromDate);
    } else if (isValidDate(safeToDate)) {
      whereCondition.created_at = LessThanOrEqual(safeToDate);
    }

    if (country_id && !isNaN(Number(country_id))) {
      whereCondition['person.countryId'] = parseInt(country_id);
    }

    if (sector_id && !isNaN(Number(sector_id))) {
      whereCondition['person.sectorId'] = parseInt(sector_id);
    }

    if (user_id) {
      whereCondition['leadUserId'] = user_id;
    }

    const leadExperts = await this.usersRepository.find({
      select: ['id', 'first_name', 'last_name'],
      where: {
        designation: 'LEADEXPERT',
      },
    });
    const generateReportForUser = async (userId: string) => {
      const total_person_assigned = await this.peopleAssignmentRepository.count(
        {
          where: {
            ...whereCondition,
            leadUserId: userId,
          },
          relations: {
            person: true,
          },
        },
      );

      const worked = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          leadUserId: userId,
          is_working_completed: true,
        },
        relations: {
          person: true,
        },
      });

      const not_worked = Math.max(0, total_person_assigned - worked);

      const email_added = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          leadUserId: userId,
          is_business_email_added: true,
        },
        relations: {
          person: true,
        },
      });

      const not_email_added = Math.max(0, total_person_assigned - email_added);

      const added_and_verified = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_verified: true,
          is_business_email_added: true,
          leadUserId: userId,
        },
        relations: {
          person: true,
        },
      });

      const replacements_added = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_business_email_added: true,
          is_replacement: true,
          leadUserId: userId,
        },
        relations: {
          person: true,
        },
      });

      const status_not_updated = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          status: AssignmentStatus.NOT_VERIFIED,
          leadUserId: userId,
        },
        relations: {
          person: true,
        },
      });

      return {
        total_person_assigned,
        worked,
        not_worked,
        email_added,
        not_email_added,
        added_and_verified,
        replacements_added,
        status_not_updated,
      };
    };

    if (user_id) {
      const userReport = await generateReportForUser(user_id);
      return {
        reports: [
          {
            ...userReport,
            user: leadExperts.find((expert) => expert.id === user_id),
          },
        ],
        overallStats: userReport,
      };
    }

    const reports = await Promise.all(
      leadExperts?.map(async (expert) => {
        const report = await generateReportForUser(expert.id);
        return {
          user: expert,
          ...report,
        };
      }),
    );

    const overallStats = reports.reduce(
      (acc, report) => {
        Object.keys(acc).forEach((key) => (acc[key] += report[key] || 0));
        return acc;
      },
      {
        total_person_assigned: 0,
        worked: 0,
        not_worked: 0,
        emails_added: 0,
        emails_not_added: 0,
        added_and_verified: 0,
        added_and_bounced: 0,
        replacements_added: 0,
        status_not_updated: 0,
      },
    );

    return {
      reports,
      overallStats,
    };
  }

  async getRegionWiseLeadsContactStats(
    queryParam: GetRegionWiseLeadsContactStatsDto,
  ) {
    const { startDate, endDate } = queryParam;

    const safeStartDate =
      typeof startDate === 'string' ? startDate.trim() : undefined;
    const safeEndDate =
      typeof endDate === 'string' ? endDate.trim() : undefined;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());
    // Helper for adding DATE() filter to QueryBuilder
    function addDateFilterToQb(
      qb: any,
      safeStartDate: string | undefined,
      safeEndDate: string | undefined,
      alias: string,
    ) {
      if (safeStartDate && safeEndDate && safeStartDate === safeEndDate) {
        qb.andWhere(`DATE(${alias}.updated_at) = :date`, {
          date: safeStartDate,
        });
      } else if (safeStartDate && safeEndDate) {
        qb.andWhere(`DATE(${alias}.updated_at) BETWEEN :start AND :end`, {
          start: safeStartDate,
          end: safeEndDate,
        });
      } else if (safeStartDate) {
        qb.andWhere(`DATE(${alias}.updated_at) >= :start`, {
          start: safeStartDate,
        });
      } else if (safeEndDate) {
        qb.andWhere(`DATE(${alias}.updated_at) <= :end`, { end: safeEndDate });
      }

      return qb;
    }

    const allCountries = await this.countryRepository.find();
    const allSectors = await this.sectorRepository.find();

    const regions = [...new Set(allCountries.map((country) => country.region))];

    let regionWiseLeads = await Promise.all(
      regions.map(async (region) => {
        const countriesInRegion = allCountries
          .filter((country) => country.region === region)
          .map((country) => country.id);

        // Company count
        const companyQb = this.companyRepository.createQueryBuilder('company');
        companyQb.where('company.countryId IN (:...countriesInRegion)', {
          countriesInRegion,
        });
        addDateFilterToQb(companyQb, safeStartDate, safeEndDate, 'company');
        const companiesCount = await companyQb.getCount();

        // People count
        const peopleQb = this.peopleRepository.createQueryBuilder('person');
        peopleQb.where('person.countryId IN (:...countriesInRegion)', {
          countriesInRegion,
        });
        addDateFilterToQb(peopleQb, safeStartDate, safeEndDate, 'person');
        const peopleCount = await peopleQb.getCount();

        // Job posts count
        const jobQb = this.jobsRepository.createQueryBuilder('job');
        jobQb.where('job.countryId IN (:...countriesInRegion)', {
          countriesInRegion,
        });
        addDateFilterToQb(jobQb, safeStartDate, safeEndDate, 'job');
        const jobPostsCount = await jobQb.getCount();

        // Basic companies count
        const basicCompanyQb =
          this.companyRepository.createQueryBuilder('company');
        basicCompanyQb.where('company.countryId IN (:...countriesInRegion)', {
          countriesInRegion,
        });
        basicCompanyQb.andWhere('company.scrapper_level = :level', {
          level: 2,
        });
        addDateFilterToQb(
          basicCompanyQb,
          safeStartDate,
          safeEndDate,
          'company',
        );
        const basicCompaniesCount = await basicCompanyQb.getCount();

        // Sector breakdown
        const sectorsData = await Promise.all(
          allSectors.map(async (sector) => {
            // Companies per sector
            const sectorCompanyQb =
              this.companyRepository.createQueryBuilder('company');
            sectorCompanyQb.where(
              'company.countryId IN (:...countriesInRegion)',
              { countriesInRegion },
            );
            sectorCompanyQb.andWhere('company.sectorId = :sectorId', {
              sectorId: sector.id,
            });
            addDateFilterToQb(
              sectorCompanyQb,
              safeStartDate,
              safeEndDate,
              'company',
            );
            const sectorCompaniesCount = await sectorCompanyQb.getCount();

            // People per sector
            const sectorPeopleQb =
              this.peopleRepository.createQueryBuilder('person');
            sectorPeopleQb.where(
              'person.countryId IN (:...countriesInRegion)',
              { countriesInRegion },
            );
            sectorPeopleQb.andWhere('person.sectorId = :sectorId', {
              sectorId: sector.id,
            });
            addDateFilterToQb(
              sectorPeopleQb,
              safeStartDate,
              safeEndDate,
              'person',
            );
            const sectorPeopleCount = await sectorPeopleQb.getCount();

            // Jobs per sector
            const sectorJobQb = this.jobsRepository.createQueryBuilder('job');
            sectorJobQb.where('job.countryId IN (:...countriesInRegion)', {
              countriesInRegion,
            });
            sectorJobQb.andWhere('job.sectorId = :sectorId', {
              sectorId: sector.id,
            });
            addDateFilterToQb(sectorJobQb, safeStartDate, safeEndDate, 'job');
            const sectorJobPostsCount = await sectorJobQb.getCount();

            // Basic companies per sector
            const basicSectorCompanyQb =
              this.companyRepository.createQueryBuilder('company');
            basicSectorCompanyQb.where(
              'company.countryId IN (:...countriesInRegion)',
              { countriesInRegion },
            );
            basicSectorCompanyQb.andWhere('company.sectorId = :sectorId', {
              sectorId: sector.id,
            });
            basicSectorCompanyQb.andWhere('company.scrapper_level = :level', {
              level: 2,
            });
            addDateFilterToQb(
              basicSectorCompanyQb,
              safeStartDate,
              safeEndDate,
              'company',
            );
            const basicSectorCompaniesCount =
              await basicSectorCompanyQb.getCount();

            return {
              id: sector.id,
              sector: sector.name,
              companies: sectorCompaniesCount,
              persons: sectorPeopleCount,
              jobposts: sectorJobPostsCount,
              basicCompanies: basicSectorCompaniesCount,
            };
          }),
        );

        return {
          region,
          companies: companiesCount,
          persons: peopleCount,
          jobposts: jobPostsCount,
          sectors: sectorsData,
          basicCompanies: basicCompaniesCount,
        };
      }),
    );

    const totalStats = regionWiseLeads.reduce(
      (acc, regionData) => {
        acc.companies += regionData.companies;
        acc.persons += regionData.persons;
        acc.jobposts += regionData.jobposts;
        acc.basicCompanies += regionData.basicCompanies;
        return acc;
      },
      { companies: 0, persons: 0, jobposts: 0, basicCompanies: 0 },
    );

    regionWiseLeads.unshift({
      region: 'Total',
      companies: totalStats.companies,
      persons: totalStats.persons,
      jobposts: totalStats.jobposts,
      basicCompanies: totalStats.basicCompanies,
      sectors: [],
    });

    return {
      regionWiseLeads,
      totalStats,
    };
  }

  async getPersonWiseLeadsStats(queryParams: GetPersonWiseLeadsStatsDto) {
    const { user_id, from_date, to_date, country_id, sector_id, industry } =
      queryParams;

    const safeStartDate =
      typeof from_date === 'string' ? from_date.trim() : undefined;
    const safeEndDate =
      typeof to_date === 'string' ? to_date.trim() : undefined;

    const whereCondition = {} as any;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
      if (safeStartDate === safeEndDate) {
        whereCondition.created_at = MoreThanOrEqual(
          `${safeStartDate}T00:00:00.000Z`,
        );
      } else {
        whereCondition.created_at = Between(
          `${safeStartDate}T00:00:00.000Z`,
          `${safeEndDate}T23:59:59.999Z`,
        );
      }
    } else if (isValidDate(safeStartDate)) {
      whereCondition.created_at = MoreThanOrEqual(
        `${safeStartDate}T00:00:00.000Z`,
      );
    } else if (isValidDate(safeEndDate)) {
      whereCondition.created_at = LessThanOrEqual(
        `${safeEndDate}T23:59:59.999Z`,
      );
    }

    if (country_id) {
      whereCondition.countryId = country_id;
    }

    if (sector_id) {
      whereCondition.sectorId = sector_id;
    }

    if (industry) {
      whereCondition.industry = industry;
    }

    if (user_id) {
      whereCondition.leadUserId = user_id;
    }

    try {
      const allPersons = await this.peopleRepository.findAndCount({
        select: ['id'],
      });

      const personIds = allPersons[0].map((person) => person.id);
      const totalPersons = allPersons[1];

      const withEmailsCount = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_business_email_added: true,
        },
      });

      const checkedEmailsCount = await this.peopleAssignmentRepository.count({
        // where: [
        //   {
        //     ...whereCondition,
        //     is_business_email_added: true,
        //   },
        //   {
        //     ...whereCondition,
        //     is_business_email_added: true,
        //     is_working_completed: true,
        //   },
        // ],
        where: {
          ...whereCondition,
          is_business_email_added: true,
          is_working_completed: true,
        },
      });

      const unCheckedEmailsCount = await this.peopleAssignmentRepository.count({
        // where: [
        //   {
        //     ...whereCondition,
        //     is_business_email_added: true,
        //   },
        //   {
        //     ...whereCondition,
        //     is_working_completed: false,
        //     is_replacement_needed: false,
        //   },
        // ],
        where: {
          ...whereCondition,
          is_business_email_added: true,
          is_working_completed: false,
          is_replacement_needed: false,
        },
      });

      const verifiedEmailsCount = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_business_email_added: true,
          is_working_completed: true,
          is_replacement_needed: false,
        },
      });

      const bouncedEmailsCount = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_business_email_added: true,
          is_replacement_needed: true,
        },
      });

      // const withoutEmailsCount = await this.peopleAssignmentRepository.count({
      //   where: {
      //     ...whereCondition,
      //     is_business_email_added: false,
      //   },
      // });

      const withoutEmailsCount = totalPersons - withEmailsCount;

      return {
        totalPersons,
        withEmailsCount,
        checkedEmailsCount,
        unCheckedEmailsCount,
        verifiedEmailsCount,
        bouncedEmailsCount,
        withoutEmailsCount,
      };
    } catch (error) {
      throw error;
    }
  }

  async getCountryAndSectorWiseLeadsByRegion(
    queryParams: GetCountryAndSectorWiseLeadsByRegionDto,
  ) {
    const { startDate, endDate, region } = queryParams;

    const safeStartDate =
      typeof startDate === 'string' ? startDate.trim() : undefined;
    const safeEndDate =
      typeof endDate === 'string' ? endDate.trim() : undefined;

    const whereCondition = {} as any;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
      if (safeStartDate === safeEndDate) {
        whereCondition.created_at = MoreThanOrEqual(
          `${safeStartDate}T00:00:00.000Z`,
        );
      } else {
        whereCondition.created_at = Between(
          `${safeStartDate}T00:00:00.000Z`,
          `${safeEndDate}T23:59:59.999Z`,
        );
      }
    } else if (isValidDate(safeStartDate)) {
      whereCondition.created_at = MoreThanOrEqual(
        `${safeStartDate}T00:00:00.000Z`,
      );
    } else if (isValidDate(safeEndDate)) {
      whereCondition.created_at = LessThanOrEqual(
        `${safeEndDate}T23:59:59.999Z`,
      );
    }

    if (region) {
      whereCondition.region = region;
    }

    try {
      const countriesList = await this.companyRepository.find({
        select: ['id', 'name', 'region'],
        where: {
          ...whereCondition,
        },
      });

      const sectorsList = await this.sectorRepository.find({
        select: ['id', 'name'],
      });

      const countryIds = countriesList.map((country) => country.id);

      const companiesData = await this.companyRepository
        .createQueryBuilder('company')
        .select('company.countryId', 'countryId')
        .addSelect('company.sectorId', 'sectorId')
        .addSelect('COUNT(company.id)', 'count')
        .where('company.countryId IN (:...countryIds)', { countryIds })
        // optionally add more `where` conditions
        .groupBy('company.countryId')
        .addGroupBy('company.sectorId')
        .getRawMany();

      const peopleData = await this.peopleRepository
        .createQueryBuilder('person')
        .select('person.countryId', 'countryId')
        .addSelect('person.sectorId', 'sectorId')
        .addSelect('COUNT(person.id)', 'count')
        .where('person.countryId IN (:...countryIds)', { countryIds })
        .groupBy('person.countryId')
        .addGroupBy('person.sectorId')
        .getRawMany();

      const jobPostsData = await this.jobsRepository
        .createQueryBuilder('job')
        .select('job.countryId', 'countryId')
        .addSelect('job.sectorId', 'sectorId')
        .addSelect('COUNT(job.id)', 'count')
        .where('job.countryId IN (:...countryIds)', { countryIds })
        .groupBy('job.countryId')
        .addGroupBy('job.sectorId')
        .getRawMany();

      const mapCounts = (data: any, type: any) => {
        const map = {};
        data.forEach(({ countryId, sectorId, count }) => {
          if (!map[countryId]) map[countryId] = {};
          if (!map[countryId][sectorId]) map[countryId][sectorId] = {};
          map[countryId][sectorId][type] = count;
        });
        return map;
      };

      const companiesMap = mapCounts(companiesData, 'companies');
      const peopleMap = mapCounts(peopleData, 'people');
      const jobPostsMap = mapCounts(jobPostsData, 'jobPosts');

      const countrywiseLeads = countriesList.map((country) => {
        const countryId = country.id;

        const sectorsData = sectorsList.map((sector) => {
          const sectorId = sector.id;
          return {
            id: sectorId,
            sector: sector.name,
            companies:
              parseInt(companiesMap[countryId]?.[sectorId]?.companies) || 0,
            persons: parseInt(peopleMap[countryId]?.[sectorId]?.persons) || 0,
            jobposts:
              parseInt(jobPostsMap[countryId]?.[sectorId]?.jobposts) || 0,
          };
        });

        return {
          id: countryId,
          country: country.country,
          companies: sectorsData.reduce((sum, s) => sum + s.companies, 0),
          persons: sectorsData.reduce((sum, s) => sum + s.persons, 0),
          jobposts: sectorsData.reduce((sum, s) => sum + s.jobposts, 0),
          sectors: sectorsData,
        };
      });

      const sectorwiseLeads: any = sectorsList.map((sector) => {
        const sectorId = sector.id;

        const companies = companiesData
          .filter((d) => d.sector_id === sectorId)
          .reduce((sum, d) => sum + parseInt(d.count), 0);

        const persons = peopleData
          .filter((d) => d.sector_id === sectorId)
          .reduce((sum, d) => sum + parseInt(d.count), 0);

        const jobposts = jobPostsData
          .filter((d) => d.sector_id === sectorId)
          .reduce((sum, d) => sum + parseInt(d.count), 0);

        return {
          id: sectorId,
          sector: sector.name,
          companies,
          persons,
          jobposts,
        };
      });

      const sectorwiseLeadsTotal = sectorwiseLeads.reduce(
        (acc, curr) => {
          acc.companies += curr.companies;
          acc.persons += curr.persons;
          acc.jobposts += curr.jobposts;
          return acc;
        },
        { companies: 0, persons: 0, jobposts: 0 },
      );

      sectorwiseLeads.push({ sector: 'Total', ...sectorwiseLeadsTotal });

      return {
        countrywiseLeads,
        sectorwiseLeads,
      };
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }

  async getScrapperReporting(queryParams: GetScrapperReportingDto) {
    const { from_date, to_date, country_id, sector_id, industry } = queryParams;

    const safeStartDate =
      typeof from_date === 'string' ? from_date.trim() : undefined;
    const safeEndDate =
      typeof to_date === 'string' ? to_date.trim() : undefined;

    const whereCondition = {} as any;

    const isValidDate = (d: string | undefined): d is string =>
      !!d && !isNaN(new Date(d).getTime());

    if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
      if (safeStartDate === safeEndDate) {
        whereCondition.created_at = MoreThanOrEqual(
          `${safeStartDate}T00:00:00.000Z`,
        );
      } else {
        whereCondition.created_at = Between(
          `${safeStartDate}T00:00:00.000Z`,
          `${safeEndDate}T23:59:59.999Z`,
        );
      }
    } else if (isValidDate(safeStartDate)) {
      whereCondition.created_at = MoreThanOrEqual(
        `${safeStartDate}T00:00:00.000Z`,
      );
    } else if (isValidDate(safeEndDate)) {
      whereCondition.created_at = LessThanOrEqual(
        `${safeEndDate}T23:59:59.999Z`,
      );
    }

    if (country_id) {
      whereCondition.countryId = country_id;
    }

    if (sector_id) {
      whereCondition.sectorId = sector_id;
    }

    if (industry) {
      whereCondition.industry = industry;
    }

    try {
      const assignedLeads = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          leadUserId: Not(IsNull()),
        },
      });

      const emailAdded = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_business_email_added: true,
          leadUserId: Not(IsNull()),
        },
      });

      const verifiedLeads = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_working_completed: true,
          leadUserId: Not(IsNull()),
        },
      });

      const bouncedLeads = await this.peopleAssignmentRepository.count({
        where: {
          ...whereCondition,
          is_replacement_needed: true,
          not_found_by: Not(IsNull()),
        },
      });

      const emailNotAdded = assignedLeads - emailAdded;

      return {
        assignedLeads,
        emailAdded,
        emailNotAdded,
        verifiedLeads,
        bouncedLeads,
      };
    } catch (error) {
      throw error;
    }
  }

  // async detailLeadsReport(queryParams: GetDetailLeadReportsDto) {
  //   const { startDate, endDate } = queryParams;
  //   const safeStartDate =
  //     typeof startDate === 'string' ? startDate.trim() : undefined;
  //   const safeEndDate =
  //     typeof endDate === 'string' ? endDate.trim() : undefined;

  //   const isValidDate = (d: string | undefined): d is string =>
  //     !!d && !isNaN(new Date(d).getTime());

  //   const whereCondition = {} as any;

  //   if (isValidDate(safeStartDate) && isValidDate(safeEndDate)) {
  //     if (safeStartDate === safeEndDate) {
  //       whereCondition.updated_at = MoreThanOrEqual(
  //         `${safeStartDate}T00:00:00.000Z`,
  //       );
  //     } else {
  //       whereCondition.updated_at = Between(
  //         `${safeStartDate}T00:00:00.000Z`,
  //         `${safeEndDate}T23:59:59.999Z`,
  //       );
  //     }
  //   } else if (isValidDate(safeStartDate)) {
  //     whereCondition.updated_at = MoreThanOrEqual(
  //       `${safeStartDate}T00:00:00.000Z`,
  //     );
  //   } else if (isValidDate(safeEndDate)) {
  //     whereCondition.updated_at = LessThanOrEqual(
  //       `${safeEndDate}T23:59:59.999Z`,
  //     );
  //   }

  //   const allCompaniesCount = await this.companyRepository.count({
  //     where: whereCondition,
  //   });
  //   const allSnrCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, sectorId: 2 },
  //   });
  //   const allDirectCompaiesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, sectorId: 1 },
  //   });
  //   const UkCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, countryId: 1 },
  //   });
  //   const UkSnrCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, countryId: 1, sectorId: 2 },
  //   });
  //   const UkDirectCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, countryId: 1, sectorId: 1 },
  //   });
  //   const UsCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, countryId: 2 },
  //   });
  //   const UsSnrCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, countryId: 2, sectorId: 2 },
  //   });
  //   const UsDirectCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, countryId: 2, sectorId: 1 },
  //   });
  //   const basicCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, scrapper_level: 2 },
  //   });
  //   const advancedCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, scrapper_level: 3 },
  //   });
  //   const jobPostScrapperCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, company_source: 'JOB_POST_SCRAPPER' },
  //   });
  //   const jobsPostsScrapperBasicCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         scrapper_level: 2,
  //       },
  //     });
  //   const jobsPostsScrapperAdvancedCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         scrapper_level: 3,
  //       },
  //     });
  //   const jobPostScrapperSnrCompaniesCount = await this.companyRepository.count(
  //     {
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         sectorId: 2,
  //       },
  //     },
  //   );
  //   const jobPostScrapperSnrBasicCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         scrapper_level: 2,
  //         sectorId: 2,
  //       },
  //     });
  //   const jobPostScrapperSnrAdvancedCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         scrapper_level: 3,
  //         sectorId: 2,
  //       },
  //     });
  //   const jobPostScrapperDirectBasicCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         scrapper_level: 2,
  //         sectorId: 1,
  //       },
  //     });
  //   const jobPostScrapperDirectAdvancedCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         scrapper_level: 3,
  //         sectorId: 1,
  //       },
  //     });
  //   const jobPostScrapperDirectCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'JOB_POST_SCRAPPER',
  //         sectorId: 1,
  //       },
  //     });
  //   const manualCompaniesCount = await this.companyRepository.count({
  //     where: { ...whereCondition, company_source: 'MANUAL' },
  //   });
  //   const manualBasicCompaniesCount = await this.companyRepository.count({
  //     where: {
  //       ...whereCondition,
  //       company_source: 'MANUAL',
  //       scrapper_level: 2,
  //     },
  //   });

  //   const manualSnrCompaniesCount = await this.companyRepository.count({
  //     where: {
  //       ...whereCondition,
  //       company_source: 'MANUAL',
  //       sectorId: 2,
  //     },
  //   });
  //   const manualSnrBasicCompaniesCount = await this.companyRepository.count({
  //     where: {
  //       ...whereCondition,
  //       company_source: 'MANUAL',
  //       scrapper_level: 2,
  //       sectorId: 2,
  //     },
  //   });
  //   const manualDirectBasicCompaniesCount = await this.companyRepository.count({
  //     where: {
  //       ...whereCondition,
  //       company_source: 'MANUAL',
  //       scrapper_level: 2,
  //       sectorId: 1,
  //     },
  //   });
  //   const manualDirectAdvancedCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'MANUAL',
  //         scrapper_level: 3,
  //         sectorId: 2,
  //       },
  //     });
  //   const manualSnrAdvancedCompaniesCount = await this.companyRepository.count({
  //     where: {
  //       ...whereCondition,
  //       company_source: 'MANUAL',
  //       scrapper_level: 3,
  //       sectorId: 2,
  //     },
  //   });
  //   const manualDirectCompaniesCount = await this.companyRepository.count({
  //     where: {
  //       ...whereCondition,
  //       company_source: 'MANUAL',
  //       sectorId: 1,
  //     },
  //   });
  //   const manualAdvancedCompaniesCount = await this.companyRepository.count({
  //     where: {
  //       ...whereCondition,
  //       company_source: 'MANUAL',
  //       scrapper_level: 3,
  //     },
  //   });
  //   const companyProfileScrapperCompaniesCount =
  //     await this.companyRepository.count({
  //       where: {
  //         ...whereCondition,
  //         company_source: 'COMPANY_PROFILE_SCRAPPER',
  //       },
  //     });

  //   const companyData = {
  //     allCompaniesCount,
  //     allSnrCompaniesCount,
  //     allDirectCompaiesCount,
  //     UkCompaniesCount,
  //     UkSnrCompaniesCount,
  //     UkDirectCompaniesCount,
  //     UsCompaniesCount,
  //     UsSnrCompaniesCount,
  //     UsDirectCompaniesCount,
  //     basicCompaniesCount,
  //     advancedCompaniesCount,
  //     jobPostScrapperCompaniesCount,
  //     jobPostScrapperSnrCompaniesCount,
  //     jobPostScrapperDirectCompaniesCount,
  //     manualCompaniesCount,
  //     manualSnrCompaniesCount,
  //     manualDirectCompaniesCount,
  //     companyProfileScrapperCompaniesCount,
  //     jobsPostsScrapperBasicCompaniesCount,
  //     jobsPostsScrapperAdvancedCompaniesCount,
  //     jobPostScrapperSnrBasicCompaniesCount,
  //     jobPostScrapperSnrAdvancedCompaniesCount,
  //     jobPostScrapperDirectBasicCompaniesCount,
  //     jobPostScrapperDirectAdvancedCompaniesCount,
  //     manualBasicCompaniesCount,
  //     manualAdvancedCompaniesCount,
  //     manualDirectBasicCompaniesCount,
  //     manualSnrBasicCompaniesCount,
  //     manualDirectAdvancedCompaniesCount,
  //     manualSnrAdvancedCompaniesCount,
  //   };

  //   const allPeopleCount = await this.peopleRepository.count({
  //     where: { ...whereCondition, person_type: PersonType.JOB_POST_LEAD },
  //   });
  //   const allSnrPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       sectorId: 2,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const allDirectPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       sectorId: 1,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const UkPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       countryId: 1,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const UkSnrPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       countryId: 1,
  //       sectorId: 2,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const UkDirectPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       countryId: 1,
  //       sectorId: 1,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const UsPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       countryId: 2,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const UsSnrPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       countryId: 2,
  //       sectorId: 2,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const UsDirectPeopleCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       countryId: 2,
  //       sectorId: 1,
  //       person_type: PersonType.JOB_POST_LEAD,
  //     },
  //   });
  //   const peopleFromLeadCount = await this.peopleRepository.count({
  //     where: { ...whereCondition, person_type: PersonType.JOB_POST_LEAD },
  //   });
  //   const peopleSnrFromLeadCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       person_type: PersonType.JOB_POST_LEAD,
  //       sectorId: 2,
  //     },
  //   });
  //   const peopleDirectFromLeadCount = await this.peopleRepository.count({
  //     where: {
  //       ...whereCondition,
  //       person_type: PersonType.JOB_POST_LEAD,
  //       sectorId: 1,
  //     },
  //   });

  //   const totalAssignedPeopleCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //       },
  //     });

  //   const totalSnrAssignedPeopleCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         sectorId: 2,
  //       },
  //     });

  //   const totalDirectAssignedPeopleCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         sectorId: 1,
  //       },
  //     });

  //   const peopleWithEmailsCount = await this.peopleAssignmentRepository.count({
  //     where: {
  //       ...whereCondition,
  //       is_business_email_added: true,
  //       is_found: true,
  //     },
  //   });

  //   const peopleSnrWithEmailsCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_business_email_added: true,
  //         is_found: true,
  //         sectorId: 2,
  //       },
  //     });

  //   const peopleDirectWithEmailsCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_business_email_added: true,
  //         is_found: true,
  //         sectorId: 1,
  //       },
  //     });

  //   const peopleWithoutEmailsCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_business_email_added: false,
  //         is_found: false,
  //       },
  //     });

  //   const peopleSnrWithoutEmailsCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_business_email_added: false,
  //         is_found: false,
  //         sectorId: 2,
  //       },
  //     });

  //   const peopleDirectWithoutEmailsCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_business_email_added: false,
  //         is_found: false,
  //         sectorId: 1,
  //       },
  //     });

  //   const peopleWithWorkingCompleted =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_working_completed: true,
  //       },
  //     });

  //   const peopleWithoutWorkingCompleted =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_working_completed: false,
  //         is_business_email_added: true,
  //       },
  //     });

  //   const peopleWithReplacementNeeded =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_business_email_added: true,
  //         is_replacement_needed: true,
  //       },
  //     });

  //   const peopleWithBouncedEmailsCount =
  //     await this.peopleAssignmentRepository.count({
  //       where: {
  //         ...whereCondition,
  //         is_bounce_back: true,
  //       },
  //     });

  //   const peopleData = {
  //     allPeopleCount,
  //     allSnrPeopleCount,
  //     allDirectPeopleCount,
  //     UkPeopleCount,
  //     UkSnrPeopleCount,
  //     UkDirectPeopleCount,
  //     UsPeopleCount,
  //     UsSnrPeopleCount,
  //     UsDirectPeopleCount,
  //     peopleFromLeadCount,
  //     peopleSnrFromLeadCount,
  //     peopleDirectFromLeadCount,
  //     totalAssignedPeopleCount,
  //     totalSnrAssignedPeopleCount,
  //     totalDirectAssignedPeopleCount,
  //     peopleWithEmailsCount,
  //     peopleSnrWithEmailsCount,
  //     peopleDirectWithEmailsCount,
  //     peopleWithoutEmailsCount,
  //     peopleSnrWithoutEmailsCount,
  //     peopleDirectWithoutEmailsCount,
  //     peopleWithWorkingCompleted,
  //     peopleWithoutWorkingCompleted,
  //     peopleWithReplacementNeeded,
  //     peopleWithBouncedEmailsCount,
  //   };

  //   const allEmailsCount = await this.personEmailRepository.count({
  //     where: { ...whereCondition },
  //   });
  //   const allPersonalEmailsCount = await this.personEmailRepository.count({
  //     where: { ...whereCondition, email_type: PersonEmailType.PERSONAL },
  //   });
  //   const allBusinessEmailsCount = await this.personEmailRepository.count({
  //     where: { ...whereCondition, email_type: PersonEmailType.BUSINESS },
  //   });
  //   const allDefaultEmailsCount = await this.personEmailRepository.count({
  //     where: { ...whereCondition, is_default: true },
  //   });
  //   const allReplacedEmailsCount = await this.personEmailRepository.count({
  //     where: { ...whereCondition, is_replaced: true },
  //   });

  //   const emailsData = {
  //     allEmailsCount,
  //     allPersonalEmailsCount,
  //     allBusinessEmailsCount,
  //     allDefaultEmailsCount,
  //     allReplacedEmailsCount,
  //   };

  //   const startOfDay = new Date();
  //   startOfDay.setHours(0, 0, 0, 0);
  //   const endOfDay = new Date();
  //   endOfDay.setHours(23, 59, 59, 999);

  //   const allJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition },
  //   });
  //   const allUkJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, countryId: 1 },
  //   });
  //   const allUsJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, countryId: 2 },
  //   });
  //   const allSnrJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, sectorId: 2 },
  //   });
  //   const allDirectJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, sectorId: 1 },
  //   });
  //   const allUkSnrJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, countryId: 1, sectorId: 2 },
  //   });
  //   const allUkDirectJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, countryId: 1, sectorId: 1 },
  //   });
  //   const allUsSnrJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, countryId: 2, sectorId: 2 },
  //   });
  //   const allUsDirectJobPostsCount = await this.jobsRepository.count({
  //     where: { ...whereCondition, countryId: 2, sectorId: 1 },
  //   });
  //   const allJobsPostedToday = await this.jobsRepository.count({
  //     where: { job_posting_date: Between(startOfDay, endOfDay) },
  //   });

  //   const jobsData = {
  //     allJobPostsCount,
  //     allUkJobPostsCount,
  //     allUsJobPostsCount,
  //     allSnrJobPostsCount,
  //     allDirectJobPostsCount,
  //     allUkSnrJobPostsCount,
  //     allUkDirectJobPostsCount,
  //     allUsSnrJobPostsCount,
  //     allUsDirectJobPostsCount,
  //     allJobsPostedToday,
  //   };

  //   const allMarketingEmailsCount = await this.mailBoxRepository.count({
  //     where: { ...whereCondition },
  //   });
  //   const allInboxMarketingEmailsCount = await this.mailBoxRepository.count({
  //     where: { ...whereCondition, type: 'INBOX' },
  //   });
  //   const allSentMarketingEmailsCount = await this.mailBoxRepository.count({
  //     where: { ...whereCondition, type: 'SENT' },
  //   });
  //   const allBouncedMarketingEmailsCount = await this.mailBoxRepository.count({
  //     where: { ...whereCondition, type: 'BOUNCE' },
  //   });
  //   const allDraftMarketingEmailsCount = await this.mailBoxRepository.count({
  //     where: { ...whereCondition, type: 'DRAFT' },
  //   });

  //   const marketingEmailsData = {
  //     allMarketingEmailsCount,
  //     allInboxMarketingEmailsCount,
  //     allSentMarketingEmailsCount,
  //     allBouncedMarketingEmailsCount,
  //     allDraftMarketingEmailsCount,
  //   };

  //   const scrapperDailyReportsData = await this.scrapperStatsRepository.find();

  //   return {
  //     companyData,
  //     peopleData,
  //     emailsData,
  //     jobsData,
  //     marketingEmailsData,
  //     scrapperDailyReportsData,
  //   };
  // }

  async detailLeadsReport(queryParams: GetDetailLeadReportsDto) {
    const { startDate, endDate } = queryParams;
    const safeStartDate =
      typeof startDate === 'string' ? startDate.trim() : undefined;
    const safeEndDate =
      typeof endDate === 'string' ? endDate.trim() : undefined;

    // Date filtering SQL logic
    const tzUpdatedAt = (alias = '') =>
      `(${alias}updated_at AT TIME ZONE 'Asia/Karachi')`;

    // Date filtering SQL logic
    const dateClause: string[] = [];
    const dateParams: any = {};

    if (safeStartDate && safeEndDate && safeStartDate === safeEndDate) {
      dateClause.push(`DATE(${tzUpdatedAt()}) = :date`);
      dateParams.date = safeStartDate;
    } else if (safeStartDate && safeEndDate) {
      dateClause.push(`DATE(${tzUpdatedAt()}) BETWEEN :start AND :end`);
      dateParams.start = safeStartDate;
      dateParams.end = safeEndDate;
    } else if (safeStartDate) {
      dateClause.push(`DATE(${tzUpdatedAt()}) >= :start`);
      dateParams.start = safeStartDate;
    } else if (safeEndDate) {
      dateClause.push(`DATE(${tzUpdatedAt()}) <= :end`);
      dateParams.end = safeEndDate;
    }

    // Helper to add date clause to a QB
    const applyDateClause = (qb: SelectQueryBuilder<any>, alias = '') => {
      if (dateClause.length > 0) {
        // Pass alias for proper table prefixing if needed
        qb.andWhere(
          dateClause[0].replace(/updated_at/g, `${alias}updated_at`),
          dateParams,
        );
      }
    };

    // Company counts
    const baseCompanyQB = () => {
      const qb = this.companyRepository.createQueryBuilder('company');
      applyDateClause(qb, 'company.');
      return qb;
    };

    const allCompaniesCount = await baseCompanyQB().getCount();
    const allSnrCompaniesCount = await baseCompanyQB()
      .andWhere('company.sectorId = 2')
      .getCount();
    const allDirectCompaiesCount = await baseCompanyQB()
      .andWhere('company.sectorId = 1')
      .getCount();

    const UkCompaniesCount = await baseCompanyQB()
      .andWhere('company.countryId = 1')
      .getCount();
    const UkSnrCompaniesCount = await baseCompanyQB()
      .andWhere('company.countryId = 1')
      .andWhere('company.sectorId = 2')
      .getCount();
    const UkDirectCompaniesCount = await baseCompanyQB()
      .andWhere('company.countryId = 1')
      .andWhere('company.sectorId = 1')
      .getCount();

    const UsCompaniesCount = await baseCompanyQB()
      .andWhere('company.countryId = 2')
      .getCount();
    const UsSnrCompaniesCount = await baseCompanyQB()
      .andWhere('company.countryId = 2')
      .andWhere('company.sectorId = 2')
      .getCount();
    const UsDirectCompaniesCount = await baseCompanyQB()
      .andWhere('company.countryId = 2')
      .andWhere('company.sectorId = 1')
      .getCount();

    const basicCompaniesCount = await baseCompanyQB()
      .andWhere('company.scrapper_level = 2')
      .getCount();
    const advancedCompaniesCount = await baseCompanyQB()
      .andWhere('company.scrapper_level = 3')
      .getCount();

    const basicManualCompaniesCount = await baseCompanyQB()
      .andWhere('company.scrapper_level = 2')
      .andWhere("company.company_source = 'MANUAL'")
      .getCount();

    const advancedManualCompaniesCount = await baseCompanyQB()
      .andWhere('company.scrapper_level = 3')
      .andWhere("company.company_source = 'MANUAL'")
      .getCount();

    const jobPostScrapperCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .getCount();
    const jobsPostsScrapperBasicCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.scrapper_level = 2')
      .getCount();
    const jobsPostsScrapperAdvancedCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.scrapper_level = 3')
      .getCount();

    const jobPostScrapperSnrCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.sectorId = 2')
      .getCount();
    const jobPostScrapperSnrBasicCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.scrapper_level = 2')
      .andWhere('company.sectorId = 2')
      .getCount();
    const jobPostScrapperSnrAdvancedCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.scrapper_level = 3')
      .andWhere('company.sectorId = 2')
      .getCount();

    const jobPostScrapperDirectBasicCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.scrapper_level = 2')
      .andWhere('company.sectorId = 1')
      .getCount();
    const jobPostScrapperDirectAdvancedCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.scrapper_level = 3')
      .andWhere('company.sectorId = 1')
      .getCount();
    const jobPostScrapperDirectCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'JOB_POST_SCRAPPER'")
      .andWhere('company.sectorId = 1')
      .getCount();

    const manualCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .getCount();
    const manualBasicCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.scrapper_level = 2')
      .getCount();
    const manualSnrCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.sectorId = 2')
      .getCount();
    const manualSnrBasicCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.scrapper_level = 2')
      .andWhere('company.sectorId = 2')
      .getCount();
    const manualDirectBasicCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.scrapper_level = 2')
      .andWhere('company.sectorId = 1')
      .getCount();
    const manualDirectAdvancedCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.scrapper_level = 3')
      .andWhere('company.sectorId = 2')
      .getCount();
    const manualSnrAdvancedCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.scrapper_level = 3')
      .andWhere('company.sectorId = 2')
      .getCount();
    const manualDirectCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.sectorId = 1')
      .getCount();
    const manualAdvancedCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'MANUAL'")
      .andWhere('company.scrapper_level = 3')
      .getCount();
    const companyProfileScrapperCompaniesCount = await baseCompanyQB()
      .andWhere("company.company_source = 'COMPANY_PROFILE_SCRAPPER'")
      .getCount();
    const companyNotScrapped = await baseCompanyQB()
      .andWhere('company.sectorId IS NULL AND company.scrapper_level = 2')
      .getCount();

    const companyData = {
      allCompaniesCount,
      allSnrCompaniesCount,
      allDirectCompaiesCount,
      UkCompaniesCount,
      UkSnrCompaniesCount,
      UkDirectCompaniesCount,
      UsCompaniesCount,
      UsSnrCompaniesCount,
      UsDirectCompaniesCount,
      basicCompaniesCount,
      advancedCompaniesCount,
      basicManualCompaniesCount,
      advancedManualCompaniesCount,
      jobPostScrapperCompaniesCount,
      jobPostScrapperSnrCompaniesCount,
      jobPostScrapperDirectCompaniesCount,
      manualCompaniesCount,
      manualSnrCompaniesCount,
      manualDirectCompaniesCount,
      companyProfileScrapperCompaniesCount,
      jobsPostsScrapperBasicCompaniesCount,
      jobsPostsScrapperAdvancedCompaniesCount,
      jobPostScrapperSnrBasicCompaniesCount,
      jobPostScrapperSnrAdvancedCompaniesCount,
      jobPostScrapperDirectBasicCompaniesCount,
      jobPostScrapperDirectAdvancedCompaniesCount,
      manualBasicCompaniesCount,
      manualAdvancedCompaniesCount,
      manualDirectBasicCompaniesCount,
      manualSnrBasicCompaniesCount,
      manualDirectAdvancedCompaniesCount,
      manualSnrAdvancedCompaniesCount,
      companyNotScrapped,
    };

    // People counts
    const basePeopleQB = () => {
      const qb = this.peopleRepository.createQueryBuilder('people');
      applyDateClause(qb, 'people.');
      return qb;
    };

    const allPeopleCount = await basePeopleQB()
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();
    const allSnrPeopleCount = await basePeopleQB()
      .andWhere('people.sectorId = 2')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();
    const allDirectPeopleCount = await basePeopleQB()
      .andWhere('people.sectorId = 1')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();

    const UkPeopleCount = await basePeopleQB()
      .andWhere('people.countryId = 1')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();
    const UkSnrPeopleCount = await basePeopleQB()
      .andWhere('people.countryId = 1')
      .andWhere('people.sectorId = 2')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();
    const UkDirectPeopleCount = await basePeopleQB()
      .andWhere('people.countryId = 1')
      .andWhere('people.sectorId = 1')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();

    const UsPeopleCount = await basePeopleQB()
      .andWhere('people.countryId = 2')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();
    const UsSnrPeopleCount = await basePeopleQB()
      .andWhere('people.countryId = 2')
      .andWhere('people.sectorId = 2')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();
    const UsDirectPeopleCount = await basePeopleQB()
      .andWhere('people.countryId = 2')
      .andWhere('people.sectorId = 1')
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .getCount();

    const peopleFromLeadCount = await basePeopleQB()
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .andWhere('people.is_hiring = :isHiring', { isHiring: true })
      .getCount();
    const peopleSnrFromLeadCount = await basePeopleQB()
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .andWhere('people.sectorId = 2')
      .andWhere('people.is_hiring = :isHiring', { isHiring: true })
      .getCount();
    const peopleDirectFromLeadCount = await basePeopleQB()
      .andWhere('people.person_type = :ptype', {
        ptype: PersonType.JOB_POST_LEAD,
      })
      .andWhere('people.sectorId = 1')
      .andWhere('people.is_hiring = :isHiring', { isHiring: true })
      .getCount();

    // Assignments
    const basePeopleAssignmentQB = () => {
      const qb = this.peopleAssignmentRepository.createQueryBuilder('pa');
      applyDateClause(qb, 'pa.');
      return qb;
    };

    const totalAssignedPeopleCount = await basePeopleAssignmentQB().getCount();
    const totalSnrAssignedPeopleCount = await basePeopleAssignmentQB()
      .andWhere('pa.sectorId = 2')
      .getCount();
    const totalDirectAssignedPeopleCount = await basePeopleAssignmentQB()
      .andWhere('pa.sectorId = 1')
      .getCount();

    const peopleWithEmailsCount = await basePeopleAssignmentQB()
      .andWhere('pa.is_business_email_added = true')
      .andWhere('pa.is_found = true')
      .getCount();
    const peopleSnrWithEmailsCount = await basePeopleAssignmentQB()
      .andWhere('pa.is_business_email_added = true')
      .andWhere('pa.is_found = true')
      .andWhere('pa.sectorId = 2')
      .getCount();
    const peopleDirectWithEmailsCount = await basePeopleAssignmentQB()
      .andWhere('pa.is_business_email_added = true')
      .andWhere('pa.is_found = true')
      .andWhere('pa.sectorId = 1')
      .getCount();

    const peopleWithoutEmailsCount = await basePeopleAssignmentQB()
      .andWhere('pa.is_business_email_added = false')
      .andWhere('pa.is_found = false')
      .getCount();
    const peopleSnrWithoutEmailsCount = await basePeopleAssignmentQB()
      .andWhere('pa.is_business_email_added = false')
      .andWhere('pa.is_found = false')
      .andWhere('pa.sectorId = 2')
      .getCount();
    const peopleDirectWithoutEmailsCount = await basePeopleAssignmentQB()
      .andWhere('pa.is_business_email_added = false')
      .andWhere('pa.is_found = false')
      .andWhere('pa.sectorId = 1')
      .getCount();

    const peopleWithWorkingCompleted = await basePeopleAssignmentQB()
      .andWhere('pa.is_working_completed = true')
      .getCount();
    const peopleWithoutWorkingCompleted = await basePeopleAssignmentQB()
      .andWhere('pa.is_working_completed = false')
      .andWhere('pa.is_business_email_added = false')
      .getCount();
    const peopleWithReplacementNeeded = await basePeopleAssignmentQB()
      .andWhere('pa.is_business_email_added = false')
      .andWhere('pa.is_replacement_needed = true')
      .getCount();
    const peopleWithBouncedEmailsCount = await basePeopleAssignmentQB()
      .andWhere('pa.is_bounce_back = true')
      .getCount();

    const peopleData = {
      allPeopleCount,
      allSnrPeopleCount,
      allDirectPeopleCount,
      UkPeopleCount,
      UkSnrPeopleCount,
      UkDirectPeopleCount,
      UsPeopleCount,
      UsSnrPeopleCount,
      UsDirectPeopleCount,
      peopleFromLeadCount,
      peopleSnrFromLeadCount,
      peopleDirectFromLeadCount,
      totalAssignedPeopleCount,
      totalSnrAssignedPeopleCount,
      totalDirectAssignedPeopleCount,
      peopleWithEmailsCount,
      peopleSnrWithEmailsCount,
      peopleDirectWithEmailsCount,
      peopleWithoutEmailsCount,
      peopleSnrWithoutEmailsCount,
      peopleDirectWithoutEmailsCount,
      peopleWithWorkingCompleted,
      peopleWithoutWorkingCompleted,
      peopleWithReplacementNeeded,
      peopleWithBouncedEmailsCount,
    };

    // Emails
    const baseEmailQB = () => {
      const qb = this.personEmailRepository.createQueryBuilder('email');
      applyDateClause(qb, 'email.');
      return qb;
    };
    const allEmailsCount = await baseEmailQB().getCount();
    const allPersonalEmailsCount = await baseEmailQB()
      .andWhere('email.email_type = :et', { et: PersonEmailType.PERSONAL })
      .getCount();
    const allBusinessEmailsCount = await baseEmailQB()
      .andWhere('email.email_type = :et', { et: PersonEmailType.BUSINESS })
      .getCount();
    const allDefaultEmailsCount = await baseEmailQB()
      .andWhere('email.is_default = true')
      .getCount();
    const allReplacedEmailsCount = await baseEmailQB()
      .andWhere('email.is_replaced = true')
      .getCount();

    const emailsData = {
      allEmailsCount,
      allPersonalEmailsCount,
      allBusinessEmailsCount,
      allDefaultEmailsCount,
      allReplacedEmailsCount,
    };

    // Jobs
    const baseJobsQB = () => {
      const qb = this.jobsRepository.createQueryBuilder('jobs');
      applyDateClause(qb, 'jobs.');
      return qb;
    };
    const allJobPostsCount = await baseJobsQB().getCount();
    const allUkJobPostsCount = await baseJobsQB()
      .andWhere('jobs.countryId = 1')
      .getCount();
    const allUsJobPostsCount = await baseJobsQB()
      .andWhere('jobs.countryId = 2')
      .getCount();
    const allSnrJobPostsCount = await baseJobsQB()
      .andWhere('jobs.sectorId = 2')
      .getCount();
    const allDirectJobPostsCount = await baseJobsQB()
      .andWhere('jobs.sectorId = 1')
      .getCount();
    const allUkSnrJobPostsCount = await baseJobsQB()
      .andWhere('jobs.countryId = 1')
      .andWhere('jobs.sectorId = 2')
      .getCount();
    const allUkDirectJobPostsCount = await baseJobsQB()
      .andWhere('jobs.countryId = 1')
      .andWhere('jobs.sectorId = 1')
      .getCount();
    const allUsSnrJobPostsCount = await baseJobsQB()
      .andWhere('jobs.countryId = 2')
      .andWhere('jobs.sectorId = 2')
      .getCount();
    const allUsDirectJobPostsCount = await baseJobsQB()
      .andWhere('jobs.countryId = 2')
      .andWhere('jobs.sectorId = 1')
      .getCount();

    // Today's jobs (no date clause, but between start of today and end of today)
    const today = new Date();
    const yyyy = today.getUTCFullYear();
    const mm = String(today.getUTCMonth() + 1).padStart(2, '0');
    const dd = String(today.getUTCDate()).padStart(2, '0');
    const todayString = `${yyyy}-${mm}-${dd}`;

    const allJobsPostedToday = await this.jobsRepository
      .createQueryBuilder('jobs')
      .where('DATE(jobs.updated_at) = :today', { today: todayString })
      .getCount();

    const jobsData = {
      allJobPostsCount,
      allUkJobPostsCount,
      allUsJobPostsCount,
      allSnrJobPostsCount,
      allDirectJobPostsCount,
      allUkSnrJobPostsCount,
      allUkDirectJobPostsCount,
      allUsSnrJobPostsCount,
      allUsDirectJobPostsCount,
      allJobsPostedToday,
    };

    // Marketing Emails
    const baseMailBoxQB = () => {
      const qb = this.mailBoxRepository.createQueryBuilder('mail');
      applyDateClause(qb, 'mail.');
      return qb;
    };
    const allMarketingEmailsCount = await baseMailBoxQB().getCount();
    const allInboxMarketingEmailsCount = await baseMailBoxQB()
      .andWhere("mail.type = 'INBOX'")
      .getCount();
    const allSentMarketingEmailsCount = await baseMailBoxQB()
      .andWhere("mail.type = 'SENT'")
      .getCount();
    const allBouncedMarketingEmailsCount = await baseMailBoxQB()
      .andWhere("mail.type = 'BOUNCE'")
      .getCount();
    const allDraftMarketingEmailsCount = await baseMailBoxQB()
      .andWhere("mail.type = 'DRAFT'")
      .getCount();

    const marketingEmailsData = {
      allMarketingEmailsCount,
      allInboxMarketingEmailsCount,
      allSentMarketingEmailsCount,
      allBouncedMarketingEmailsCount,
      allDraftMarketingEmailsCount,
    };

    // Scrapper daily reports
    const scrapperDailyReportsData = await this.scrapperStatsRepository.find();

    const todayDate = new Date();
    const formatted =
      todayDate.getFullYear() +
      '-' +
      String(today.getMonth() + 1).padStart(2, '0') +
      '-' +
      String(today.getDate()).padStart(2, '0');

    const companyScrapperReportsData =
      await this.companyScrapperRepository.find({
        where: {
          date: formatted,
        },
      });

    return {
      companyData,
      peopleData,
      emailsData,
      jobsData,
      marketingEmailsData,
      scrapperDailyReportsData,
      companyScrapperReportsData,
    };
  }
}
