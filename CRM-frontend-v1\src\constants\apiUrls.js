// const api_url = 'https://19cxs75g-5001.inc1.devtunnels.ms/api/';

const api_url = 'http://localhost:5001/api/';
export const API_URLS = {
  // --------------------------------------------------------AUTH--------------------------------------------------------
  uploadImage: `${api_url}upload/image`,
  login: `${api_url}auth/login`,
  register: `${api_url}auth/register`,
  verifyEmail: `${api_url}auth/verify-email`,
  resendVerificationEmail: `${api_url}auth/resend-verification-email`,
  verify2FACode: `${api_url}auth/verify`,
  sendVerificationCode: `${api_url}auth/send-verification-code`,
  sendResetPasswordLink: `${api_url}auth/forgot-password`,
  resetPassword: `${api_url}auth/reset-password`,
  getAllUsers: `${api_url}auth/get-all-users`,
  updateUserStatus: `${api_url}auth/update-status`,
  updateUserData: `${api_url}auth/update-user/:id`,
  getDesignationBasedUsers: `${api_url}auth/designationBasedUsers`,

  // -------------------------------------------------------- SERVICES --------------------------------------------------------
  getAllServices: `${api_url}service/getAll`,

  //--------------------------------------------------------- INDUSTRIES ------------------------------------------------------

  getAllIndustries: 'https://19cxs75g-5000.inc1.devtunnels.ms/api/companies/getUniqueIndustriesFromCompanies',

  // -------------------------------------------------------- ROLES MANAGEMENT --------------------------------------------------------
  addRole: `${api_url}roles/create`,
  getServiceBasedRoleStats: `${api_url}roles/service/stats`,
  getCVSourcingRoles: `${api_url}roles/findCvSourcingRoles`,
  totalTrialRoles: `${api_url}roles/totalTrialRoles`,
  getRolesByServiceId: `${api_url}roles/find360_PreQualificationRoles`,
  getRoleById: `${api_url}roles/find/:id`,
  editRoleById: `${api_url}roles/update/:id`,

  // -------------------------------------------------------- ROLE Logs--------------------------------------------------------
  startRole: `${api_url}role-logs/start-role`,
  markRoleAsDone: `${api_url}role-logs/mark-role-done`,
  leaveRole: `${api_url}role-logs/leave-role`,
  getTotalTrialLogs: `${api_url}role-logs/getTotalTrialLogs`,
  getRoleActivity: `${api_url}role-logs/role-activity/:roleId`,
  changeRoleLogStatus: `${api_url}role-logs/change-role-status/:roleLogId`,

  // -------------------------------------------------------- ROLE SEQUENCES --------------------------------------------------------
  createSequence: `${api_url}sequence/create`,
  getAllSequences: `${api_url}sequence/get-all`,
  getSequenceById: `${api_url}sequence/:id`,
  updateSequence: `${api_url}sequence/:id`,
  deleteSequence: `${api_url}sequence/:id`,
  startSequence: `${api_url}sequence/:id/start`,
  getSequenceWithSteps: `${api_url}sequence/:id/with-steps`,
  getSequenceStats: `${api_url}sequence/:id/stats`,

  // -------------------------------------------------------- FOLLOW-UP SEQUENCES --------------------------------------------------------
  createFollowUpSteps: `${api_url}sequence/steps/:stepId/follow-up`,
  getFollowUpSteps: `${api_url}sequence/steps/:stepId/follow-up`,
  triggerManualFollowUp: `${api_url}sequence/candidate/:candidateId/step/:stepId/trigger-follow-up`,
  hasRespondedToSequence: `${api_url}sequence/candidate/:candidateId/sequence/:sequenceId/has-responded`,

  // -------------------------------------------------------- SEQUENCE STEPS --------------------------------------------------------
  createSequenceStep: `${api_url}sequence-steps/create`,
  getAllSequenceSteps: `${api_url}sequence-steps/get-all`,
  getSequenceStepById: `${api_url}sequence-steps/:id`,
  updateSequenceStep: `${api_url}sequence-steps/:id`,
  deleteSequenceStep: `${api_url}sequence-steps/:id`,

  // -------------------------------------------------------- QUEUE MANAGEMENT --------------------------------------------------------
  getQueueStats: `${api_url}queue/stats`,
  getQueueStatsByName: `${api_url}queue/stats/:queueName`,
  pauseQueue: `${api_url}queue/:queueName/pause`,
  resumeQueue: `${api_url}queue/:queueName/resume`,
  addTestJob: `${api_url}queue/:queueName/test`,

  // -------------------------------------------------------- WEBHOOKS --------------------------------------------------------
  emailWebhook: `${api_url}webhooks/email`,
  whatsappWebhook: `${api_url}webhooks/whatsapp`,
  smsWebhook: `${api_url}webhooks/sms`,
  callWebhook: `${api_url}webhooks/call`,
  linkedinWebhook: `${api_url}webhooks/linkedin`,
  testWebhook: `${api_url}webhooks/test/:candidateId/:stepId`,

  // -------------------------------------------------------- CANDIDATE SEQUENCE STATUS --------------------------------------------------------
  getCandidateSequenceStatus: `${api_url}candidate-sequence-status/candidate/:candidateId/sequence/:sequenceId`,
  getStatusByType: `${api_url}candidate-sequence-status/status/:status`,

  // ------------------------------------------------------- ROLE FOCUS POINTS ------------------------------------------------------------

  addFocusPoint: `${api_url}focus-point/create`,
  getRoleFocusPoints: `${api_url}focus-point/all`,

  // -------------------------------------------------------- PROSPECTS --------------------------------------------------------

  getServiceBasedProspectsStats: `${api_url}people/prospect/stats/service`,
  getAllUserProspects: `${api_url}people/prospect/all-user-added`,
  addNewProspect: `${api_url}people/create`,
  getPersonByPersonType: `${api_url}people/person-by-person-type`,
  getPartiallyInterestedProspects: `${api_url}people/get-partially-intrested-prospects`,
  getTrialProspects: `${api_url}people/getTrialProspects`,
  getInFutureProspects: `${api_url}people/getInFutureProspects`,
  getConvertedProspects: `${api_url}people/getConvertedProspects`,

  // -------------------------------------------------------- INVOICES --------------------------------------------------------
  createInvoice: `${api_url}invoices/create`,
  getAllInvoices: `${api_url}invoices/all`,
  getInvoicesStats: `${api_url}invoices/invoiceStatsGroupByStatus`,
  updateInvoice: `${api_url}invoices/:id`,
  invoicesForBdDashboard: `${api_url}invoices/invoicesForBdDashboard`,

  // -------------------------------------------------------- S3 FILE UPLOADS --------------------------------------------------------
  uploadFileToS3Bucket: `${api_url}s3bucket/upload`,
  downloadFileFromS3Bucket: `${api_url}s3bucket/downloadFile`,
  downloadAllCandidates: `${api_url}s3bucket/download-all-cvs`,

  // -------------------------------------------------------- EMAIL TEMPLATES --------------------------------------------------------
  createTemplate: `${api_url}email-templates/create`,
  getAllTemplates: `${api_url}email-templates`,
  getTemplatesByType: `${api_url}email-templates/:type`,

  // -------------------------------------------------------- CALENDAR --------------------------------------------------------
  createEvent: `${api_url}calendar/create`,
  getEventByEventType: `${api_url}calendar/getByEventType/:eventType/:userId`,
  updateEvent: `${api_url}calendar/update/{id}`,

  // -------------------------------------------------------- COUNTRIES --------------------------------------------------------
  getAllCountries: `${api_url}country/findAll`,

  // -------------------------------------------------------- MAILBOX --------------------------------------------------------
  getAllEmailsByEmailType: `${api_url}mail-box/email`,
  updateEmailReadStatus: `${api_url}mail-box/update-read-status/:id`,
  assignProspectToBd: `${api_url}mail-box/update-assign-email-to-bd`,
  getMyTrialLeads: `${api_url}mail-box/my-trial-leads/:assigneeId`,
  updateEmailProspectStatus: `${api_url}mail-box/update-prospect-status`,
  saveEmailAsDraft: `${api_url}mail-box/save-as-draft`,
  scheduleEmail: `${api_url}mail-box/schedule-email`,

  // -------------------------------------------------------- CLIENTS --------------------------------------------------------
  getServiceBasedClientStats: `${api_url}people/client/stats/service`,
  getAllUserClients: `${api_url}people/client/all-user-added`,
  renewClientService: `${api_url}people/client/renew/:id`,
  bdDashboard: `${api_url}people/getBdDashboard`,
  rolesDataForBdDashboard: `${api_url}roles/rolesDataForBdDashboard`,
  recentRoleActivityBdDashboard: `${api_url}role-logs/recent-role-activity-for-bd-dashboard`,

  // -------------------------------------------------------- SEND EMAIL --------------------------------------------------------
  sendEmail: `${api_url}email/send`,
  sendEmailWithAttachments: `${api_url}email/send-email-with-attachment`,

  // -------------------------------------------------------- ROLE CVS MANAGEMENT --------------------------------------------------------
  // addCVToRole: `${api_url}role-cvs/create`,

  // -------------------------------------------------------- ROLE CANDIDATE --------------------------------------------------------
  addLisToRole: `${api_url}role-candidates/add-linkedin-to-role`,
  addCVToRole: `${api_url}role-candidates/add-cv-to-role`,
  getRoleCandidates: `${api_url}role-candidates/get-role-candidates`,
  changeCandidateStatus: `${api_url}role-candidates/change-role-candidate-status`,
  deleteRoleCandidate: `${api_url}role-candidates/delete-role-candidate`,
  getRoleCandidateCount: `${api_url}role-candidates/get-role-candidate-count/:roleId`,
  get360AndPreQualificationCandidates: `${api_url}role-candidates/get-360-and-pre-qualification-candidates/:roleId`,

  // -------------------------------------------------------- Whatsapp handling --------------------------------------------------------
  sendWhatsappMessage: `${api_url}twillio/whatsapp/send`,
  getWhatsappMessagesByPhoneNumber: `${api_url}twillio/whatsapp/messages/:phoneNumber`,
  sendWAEmailCampaign: `${api_url}twillio/sendWAEmailCampaign`,

  // -------------------------------------------------------- SMS GENERATION --------------------------------------------------------
  sendSmsMessage: `${api_url}twillio/sms/send`,
  getSmsMessagesByPhoneNumber: `${api_url}twillio/sms/messages/:phoneNumber`,
  generateSMS: `${api_url}twillio/sms/getSmsMessagesByPhoneNumber`,

  // -------------------------------------------------------- SMS GENERATION --------------------------------------------------------
  getCandidateBySearchString: `${api_url}people/getCandidateBySearchString`,

  // -------------------------------------------------------- 360 <USER> <GROUP>
  updateRoleCandidateStage: `${api_url}recruitment/update-role-candidate-stage`
};

//-------------------------------------------------------- GENERATE BOOLEAN STRING URLS --------------------------------------------------------
export const PYTHON_API_URL = {
  generateBooleanString: `https://19cxs75g-8000.inc1.devtunnels.ms/generate_boolean`,
  generateKeywords: `https://19cxs75g-8000.inc1.devtunnels.ms/generate_filter`,
  chatAI: `https://19cxs75g-5001.inc1.devtunnels.ms/api/prompt`,
  extractData: `https://19cxs75g-8000.inc1.devtunnels.ms/upload`
};
