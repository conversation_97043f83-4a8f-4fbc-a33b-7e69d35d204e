import React, { useState, useEffect } from 'react';
import {
  Card,
  Timeline,
  Tag,
  Space,
  Button,
  Modal,
  Descriptions,
  Table,
  Progress,
  Row,
  Col,
  Statistic,
  Alert,
  Tooltip,
  Badge
} from 'antd';
import {
  MailOutlined,
  MessageOutlined,
  PhoneOutlined,
  LinkedinOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  SendOutlined,
  UserOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { sequenceAnalyticsAPI } from '../api/sequenceAnalytics';

const CandidateJourneyView = ({ candidateId, sequenceId, visible, onClose }) => {
  const [journeyData, setJourneyData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [stepDetails, setStepDetails] = useState([]);

  useEffect(() => {
    if (visible && candidateId && sequenceId) {
      loadCandidateJourney();
    }
  }, [visible, candidateId, sequenceId]);

  const loadCandidateJourney = async () => {
    try {
      setLoading(true);
      const journey = await sequenceAnalyticsAPI.getCandidateJourney(candidateId, sequenceId);
      setJourneyData(journey);
      
      // Mock step details - in real implementation, this would come from the API
      setStepDetails([
        {
          id: 1,
          name: 'Initial Email Outreach',
          medium: 'EMAIL',
          status: 'DELIVERED',
          sentAt: '2024-01-15T10:00:00Z',
          deliveredAt: '2024-01-15T10:01:00Z',
          openedAt: '2024-01-15T14:30:00Z',
          template: 'Welcome Email Template',
          subject: 'Exciting Opportunity at TechCorp'
        },
        {
          id: 2,
          name: 'Follow-up Email',
          medium: 'EMAIL',
          status: 'SENT',
          sentAt: '2024-01-17T09:00:00Z',
          template: 'Follow-up Email Template',
          subject: 'Following up on our opportunity'
        },
        {
          id: 3,
          name: 'SMS Follow-up',
          medium: 'SMS',
          status: 'QUEUED',
          scheduledFor: '2024-01-19T10:00:00Z',
          template: 'SMS Follow-up Template'
        }
      ]);
    } catch (error) {
      console.error('Error loading candidate journey:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStepIcon = (medium, status) => {
    const icons = {
      'EMAIL': <MailOutlined />,
      'SMS': <MessageOutlined />,
      'WHATSAPP': <MessageOutlined />,
      'LINKEDIN': <LinkedinOutlined />,
      'CALL': <PhoneOutlined />
    };

    const statusColors = {
      'DELIVERED': '#52c41a',
      'SENT': '#1890ff',
      'OPENED': '#722ed1',
      'REPLIED': '#13c2c2',
      'FAILED': '#ff4d4f',
      'QUEUED': '#faad14'
    };

    return (
      <div style={{ 
        color: statusColors[status] || '#d9d9d9',
        fontSize: '16px'
      }}>
        {icons[medium] || <MailOutlined />}
      </div>
    );
  };

  const getStatusColor = (status) => {
    const colors = {
      'DELIVERED': 'green',
      'SENT': 'blue',
      'OPENED': 'purple',
      'REPLIED': 'cyan',
      'FAILED': 'red',
      'QUEUED': 'orange',
      'NOT_STARTED': 'default'
    };
    return colors[status] || 'default';
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  const calculateProgress = () => {
    const totalSteps = stepDetails.length;
    const completedSteps = stepDetails.filter(step => 
      ['DELIVERED', 'OPENED', 'REPLIED'].includes(step.status)
    ).length;
    
    return totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  };

  const renderTimelineItem = (step, index) => {
    const isCompleted = ['DELIVERED', 'OPENED', 'REPLIED'].includes(step.status);
    const isFailed = step.status === 'FAILED';
    const isPending = ['QUEUED', 'NOT_STARTED'].includes(step.status);

    return (
      <Timeline.Item
        key={step.id}
        dot={getStepIcon(step.medium, step.status)}
        color={isCompleted ? 'green' : isFailed ? 'red' : isPending ? 'blue' : 'gray'}
      >
        <Card size="small" style={{ marginBottom: 8 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div>
              <strong>{step.name}</strong>
              <div style={{ marginTop: 4 }}>
                <Tag color={getStatusColor(step.status)}>{step.status}</Tag>
                <Tag>{step.medium}</Tag>
              </div>
            </div>
            <div style={{ textAlign: 'right' }}>
              {step.sentAt && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Sent: {formatDateTime(step.sentAt)}
                </div>
              )}
              {step.deliveredAt && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Delivered: {formatDateTime(step.deliveredAt)}
                </div>
              )}
              {step.openedAt && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Opened: {formatDateTime(step.openedAt)}
                </div>
              )}
              {step.scheduledFor && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  Scheduled: {formatDateTime(step.scheduledFor)}
                </div>
              )}
            </div>
          </div>
          
          {step.subject && (
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              Subject: {step.subject}
            </div>
          )}
          
          {step.template && (
            <div style={{ marginTop: 4, fontSize: '12px', color: '#666' }}>
              Template: {step.template}
            </div>
          )}
        </Card>
      </Timeline.Item>
    );
  };

  const renderCandidateInfo = () => (
    <Card title="Candidate Information" style={{ marginBottom: 16 }}>
      <Descriptions column={2} size="small">
        <Descriptions.Item label="Candidate ID">{candidateId}</Descriptions.Item>
        <Descriptions.Item label="Sequence ID">{sequenceId}</Descriptions.Item>
        <Descriptions.Item label="Status">
          <Tag color={getStatusColor(journeyData?.status)}>
            {journeyData?.status || 'UNKNOWN'}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Started">
          {formatDateTime(journeyData?.startedAt)}
        </Descriptions.Item>
        <Descriptions.Item label="Last Activity">
          {formatDateTime(journeyData?.lastActivity)}
        </Descriptions.Item>
        <Descriptions.Item label="Current Step">
          {journeyData?.currentStep || 'Not Started'}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );

  const renderProgressOverview = () => {
    const progress = calculateProgress();
    const completedSteps = stepDetails.filter(step => 
      ['DELIVERED', 'OPENED', 'REPLIED'].includes(step.status)
    ).length;
    const failedSteps = stepDetails.filter(step => step.status === 'FAILED').length;
    const pendingSteps = stepDetails.filter(step => 
      ['QUEUED', 'NOT_STARTED'].includes(step.status)
    ).length;

    return (
      <Card title="Journey Progress" style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="Progress"
              value={progress}
              suffix="%"
              prefix={<CheckCircleOutlined />}
            />
            <Progress percent={progress} strokeColor="#52c41a" />
          </Col>
          <Col span={6}>
            <Statistic
              title="Completed"
              value={completedSteps}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Failed"
              value={failedSteps}
              prefix={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="Pending"
              value={pendingSteps}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <Modal
      title={`Candidate Journey - ID: ${candidateId}`}
      visible={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>
      ]}
      loading={loading}
    >
      {journeyData && (
        <div>
          {renderCandidateInfo()}
          {renderProgressOverview()}
          
          <Card title="Journey Timeline">
            <Timeline mode="left">
              {stepDetails.map((step, index) => renderTimelineItem(step, index))}
            </Timeline>
          </Card>

          {journeyData?.status === 'FAILED' && (
            <Alert
              message="Journey Failed"
              description="This candidate's journey has failed. Check the timeline above for details."
              type="error"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}

          {journeyData?.status === 'REPLIED' && (
            <Alert
              message="Candidate Responded!"
              description="This candidate has responded to the sequence. All follow-up steps have been automatically stopped."
              type="success"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </div>
      )}
    </Modal>
  );
};

export default CandidateJourneyView;
