import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { PeopleService } from './people.service';
import {
  ApiBody,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UpdatePeopleDTO } from './dto/updatePeople.dto';
import { GetAllPersonsDto, PeopleDto } from './dto/createPeople.dto';
import { RenewClientDto } from './dto/renewClient.dto';
import { PersonType } from './dto/people.enums';
import { CreatePersonWithCompanyDto } from './dto/PersonWithCompany.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { People } from './people.entity';

@Controller('people')
@ApiTags('people')
export class PeopleController {
  constructor(private readonly peopleService: PeopleService) {}

  @Post('create')
  @ApiOperation({ summary: 'Create a new person' })
  async createPerson(@Body() body: any) {
    const {
      company_name,
      company_link,
      email,
      phone_number,
      company_size,
      ...peopleFields
    } = body;

    return this.peopleService.create(
      peopleFields, // This becomes PeopleDto
      company_name,
      company_link,
      email,
      phone_number,
      company_size,
    );
  }

  @Post('addPeopleInBulk')
  @ApiOperation({ summary: 'Add people in bulk' })
  async addPeopleInBulk(@Body() body: PeopleDto[]) {
    return this.peopleService.addPeopleInBulk(body);
  }

  @Post('upload-csv')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: diskStorage({
        destination: './csv-uploads',
        filename: (req, file, cb) => {
          const originalName = file.originalname;
          const extension = extname(originalName);
          const fileName = Date.now() + extension;
          cb(null, fileName);
        },
      }),
    }),
  )
  async uploadCsv(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new Error('No file uploaded');
    }
    return this.peopleService.importPeopleFromCsv(file.path);
  }

  @Post('updatePerson')
  @ApiOperation({ summary: 'Update a person' })
  async updateLiCandidate(@Body() body: any) {
    // console.log('body', body);
    const id = body.id; // Assuming `id` is a property of `body`
    return this.peopleService.updateLiCandidate(id, body);
  }

  @Put('update/:id')
  @ApiOperation({ summary: 'Update a person' })
  async updatePerson(@Param('id') id: number, @Body() body: UpdatePeopleDTO) {
    return this.peopleService.update(id, body);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all people' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: 'Page size',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term',
    type: String,
  })
  @ApiQuery({
    name: 'person_type',
    required: false,
    description: 'Person type',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of people',
  })
  async findAll(
    @Query('page') page: number,
    @Query('pageSize') pageSize: number,
    @Query('search') search: string,
    @Query('person_type') person_type: PersonType,
  ) {
    return this.peopleService.findAll(page, pageSize, search, person_type);
  }

  @Get('getAllPersons')
  async getAllPersons(@Query() queryParams: GetAllPersonsDto) {
    return this.peopleService.getAllPersons(queryParams);
  }

  @Get('getPersonByCompanyId')
  async findPersonByCompanyId(@Query('company_id') company_id: string) {
    console.log('I am called: ' + company_id);
    return this.peopleService.findPersonByCompanyId(company_id);
  }

  @Get('getUniquePersonTitles')
  async findUniquePersonTitles() {
    return this.peopleService.findUniquePersonTitles();
  }

  @Get('searchPersons')
  async findSearchPersons(@Query('search') search: string) {
    return this.peopleService.findPersons(search);
  }

  @Get('searchPersonsOfCompany')
  async findPersonsOfCompany(
    @Query('company_id') company_id: string,
    @Query('search') search: string,
  ) {
    return this.peopleService.findPersonsByCompany(company_id, search);
  }

  @Get('find/:id')
  @ApiOperation({ summary: 'Get a person by id' })
  async findOne(@Param('id') id: number) {
    return this.peopleService.findOne(id);
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete a person by id' })
  async deletePerson(@Param('id') id: number) {
    return this.peopleService.delete(id);
  }

  // Client Routes
  @Get('client/all-user-added')
  @ApiOperation({ summary: 'Get all clients' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: 'Page size',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term',
    type: String,
  })
  @ApiQuery({
    name: 'acmUserId',
    required: false,
    description: 'ACM user ID',
    type: String,
  })
  @ApiQuery({
    name: 'bdUserId',
    required: false,
    description: 'BD user ID',
    type: String,
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    description: 'Start date',
    type: Date,
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    description: 'End date',
    type: Date,
  })
  @ApiResponse({
    status: 200,
    description: 'List of clients',
  })
  @ApiQuery({
    name: 'serviceId',
    required: false,
    description: 'Service Id',
    type: Number,
  })
  @ApiResponse({
    status: 404,
    description: 'No clients found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findAllClients(
    @Query('page') page: number,
    @Query('pageSize') pageSize: number,
    @Query('search') search: string,
    @Query('acmUserId') acmUserId: string,
    @Query('bdUserId') bdUserId: string,
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
    @Query('serviceId') serviceId: number,
  ) {
    return this.peopleService.findAllClients(
      page,
      pageSize,
      search,
      acmUserId,
      bdUserId,
      start_date,
      end_date,
      serviceId,
    );
  }

  @Get('client/:id')
  @ApiOperation({ summary: 'Get a client by ID' })
  @ApiResponse({
    status: 200,
    description: 'Client details',
  })
  @ApiResponse({
    status: 404,
    description: 'Client not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findClientById(@Param('id') id: number) {
    return this.peopleService.findClientById(id);
  }

  @Get('client/service/:serviceId')
  @ApiOperation({ summary: 'Get all clients by service ID' })
  @ApiResponse({
    status: 200,
    description: 'List of clients by service ID',
  })
  @ApiResponse({
    status: 404,
    description: 'No clients found for the given service ID',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findClientByServiceId(@Param('serviceId') serviceId: number) {
    return this.peopleService.findClientByServiceId(serviceId);
  }

  @Get('client/:client_number')
  @ApiOperation({ summary: 'Get client service by ID' })
  @ApiResponse({
    status: 200,
    description: 'Client service details',
  })
  @ApiResponse({
    status: 404,
    description: 'Client service not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findClientServiceById(@Param('client_number') client_number: string) {
    return this.peopleService.findClientByClientNumber(client_number);
  }

  @Put('client/renew/:id')
  @ApiOperation({ summary: 'Renew client service' })
  @ApiResponse({
    status: 200,
    description: 'Client service renewed successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Client service not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async renewClient(@Param('id') id: number, @Body() body: RenewClientDto) {
    return this.peopleService.renewClient(id, body);
  }

  @Put('client/update-status/:id')
  @ApiOperation({ summary: 'Update client status' })
  async updateClientStatus(
    @Param('id') id: number,
    @Body('status') status: string,
  ) {
    return this.peopleService.updateClientStatus(id, status);
  }

  @Get('client/stats/service')
  @ApiOperation({ summary: 'Get client service stats' })
  @ApiResponse({
    status: 200,
    description: 'Client service stats',
  })
  @ApiResponse({
    status: 404,
    description: 'No client service stats found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    description: 'Start date',
    type: Date,
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    description: 'End date',
    type: Date,
  })
  @ApiQuery({
    name: 'service_id',
    required: false,
    description: 'Service ID',
    type: Number,
  })
  @ApiQuery({
    name: 'bdUserId',
    required: false,
    description: 'BD user ID',
    type: String,
  })
  @ApiQuery({
    name: 'acmUserId',
    required: false,
    description: 'ACM user ID',
    type: String,
  })
  async getClientServiceStats(
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
    @Query('service_id') service_id: number,
    @Query('bdUserId') bdUserId: string,
    @Query('acmUserId') acmUserId: string,
  ) {
    return this.peopleService.getClientServiceStats(
      start_date,
      end_date,
      service_id,
      bdUserId,
      acmUserId,
    );
  }

  // Prospect Routes
  @Get('prospect/all-user-added')
  @ApiOperation({ summary: 'Get all prospects' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: 'Page size',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term',
    type: String,
  })
  @ApiQuery({
    name: 'acmUserId',
    required: false,
    description: 'ACM user ID',
    type: Number,
  })
  @ApiQuery({
    name: 'bdUserId',
    required: false,
    description: 'BD user ID',
    type: Number,
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    description: 'Start date',
    type: Date,
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    description: 'End date',
    type: Date,
  })
  @ApiQuery({
    name: 'serviceId',
    required: false,
    description: 'Service Id',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'List of prospects',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospects found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findAllProspects(
    @Query('page') page: number,
    @Query('pageSize') pageSize: number,
    @Query('search') search: string,
    @Query('acmUserId') acmUserId: string,
    @Query('bdUserId') bdUserId: string,
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
    @Query('serviceId') serviceId: number,
  ) {
    return this.peopleService.findAllProspects(
      page,
      pageSize,
      search,
      acmUserId,
      bdUserId,
      start_date,
      end_date,
      serviceId,
    );
  }
  @Get('prospect/:id')
  @ApiOperation({ summary: 'Get a prospect by ID' })
  @ApiResponse({
    status: 200,
    description: 'Prospect details',
  })
  @ApiResponse({
    status: 404,
    description: 'Prospect not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findProspectById(@Param('id') id: number) {
    return this.peopleService.findProspectById(id);
  }
  @Get('prospect/service/:serviceId')
  @ApiOperation({ summary: 'Get all prospects by service ID' })
  @ApiResponse({
    status: 200,
    description: 'List of prospects by service ID',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospects found for the given service ID',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findProspectByServiceId(@Param('serviceId') serviceId: number) {
    return this.peopleService.findProspectByServiceId(serviceId);
  }
  @Get('prospect/:client_number')
  @ApiOperation({ summary: 'Get prospect service by ID' })
  @ApiResponse({
    status: 200,
    description: 'Prospect service details',
  })
  @ApiResponse({
    status: 404,
    description: 'Prospect service not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async findProspectServiceById(@Param('client_number') client_number: string) {
    return this.peopleService.findProspectByClientNumber(client_number);
  }
  @Put('prospect/renew/:id')
  @ApiOperation({ summary: 'Renew prospect service' })
  @ApiResponse({
    status: 200,
    description: 'Prospect service renewed successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Prospect service not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async renewProspect(@Param('id') id: number, @Body() body: RenewClientDto) {
    return this.peopleService.renewProspect(id, body);
  }
  @Put('prospect/update-status/:id')
  @ApiOperation({ summary: 'Update prospect status' })
  @ApiBody({
    description: 'Status to update',
    type: String,
    required: true,
    schema: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          description: 'New status',
          example: 'active',
        },
      },
    },
  })
  async updateProspectStatus(
    @Param('id') id: number,
    @Body('status') status: string,
  ) {
    return this.peopleService.updateProspectStatus(id, status);
  }
  @Get('prospect/stats/service')
  @ApiOperation({ summary: 'Get prospect service stats' })
  @ApiResponse({
    status: 200,
    description: 'Prospect service stats',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospect service stats found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @ApiQuery({
    name: 'start_date',
    required: false,
    description: 'Start date',
    type: Date,
  })
  @ApiQuery({
    name: 'end_date',
    required: false,
    description: 'End date',
    type: Date,
  })
  @ApiQuery({
    name: 'service_id',
    required: false,
    description: 'Service ID',
    type: Number,
  })
  @ApiQuery({
    name: 'bdUserId',
    required: false,
    description: 'BD user ID',
    type: String,
  })
  @ApiQuery({
    name: 'acmUserId',
    required: false,
    description: 'ACM user ID',
    type: String,
  })
  async getProspectServiceStats(
    @Query('start_date') start_date: Date,
    @Query('end_date') end_date: Date,
    @Query('service_id') service_id: number,
    @Query('bdUserId') bdUserId: string,
    @Query('acmUserId') acmUserId: string,
  ) {
    return this.peopleService.getProspectServiceStats(
      start_date,
      end_date,
      service_id,
      bdUserId,
      acmUserId,
    );
  }

  @Get('prospect/subscriptionStatus/:id')
  @ApiOperation({ summary: 'Get prospect subscription status' })
  @ApiResponse({
    status: 200,
    description: 'Prospect subscription status',
  })
  @ApiResponse({
    status: 404,
    description: 'Prospect subscription status not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getProspectSubscriptionStatus(
    @Param('prospect_status') prospect_status: string,
  ) {
    return this.peopleService.getProspectSubscriptionStatus(prospect_status);
  }

  @Put('prospect/update-subscription-status/:id')
  @ApiOperation({ summary: 'Update prospect subscription status' })
  async updateProspectSubscriptionStatus(
    @Param('id') id: number,
    @Body('prospect_status') prospect_status: string,
  ) {
    return this.peopleService.updateProspectStatusById(id, prospect_status);
  }

  @Get('prospect/status')
  @ApiOperation({ summary: 'Get all prospect statuses' })
  @ApiQuery({
    name: 'prospect_status',
    required: false,
    description: 'Prospect status',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of prospect statuses',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospect statuses found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getAllProspectStatuses(
    @Query('prospect_status') prospect_status: string,
  ) {
    return this.peopleService.getProspectByProspectStatus(prospect_status);
  }

  // updateProspectStatusByClientNumberAndServiceId

  @Put('prospect/update-status-by-client-number-and-service-id')
  @ApiOperation({
    summary: 'Update prospect status by client number and service ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Prospect status updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Prospect status not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @ApiQuery({
    name: 'client_number',
    required: true,
    description: 'Client number',
    type: String,
  })
  @ApiQuery({
    name: 'service_id',
    required: true,
    description: 'Service ID',
    type: Number,
  })
  @ApiQuery({
    name: 'prospect_status',
    required: true,
    description: 'Prospect status',
    type: String,
  })
  async updateProspectStatusByClientNumberAndServiceId(
    @Body('client_number') client_number: string,
    @Body('service_id') service_id: number,
    @Body('prospect_status') prospect_status: string,
  ) {
    return this.peopleService.updateProspectStatusByClientNumberAndServiceId(
      client_number,
      service_id,
      prospect_status,
    );
  }

  @Get('person/:userId')
  @ApiOperation({ summary: 'Get client by user ID' })
  @ApiResponse({
    status: 200,
    description: 'Client details',
  })
  @ApiResponse({
    status: 404,
    description: 'Client not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getUserPerson(@Param('userId') userId: string) {
    return this.peopleService.getPersonByUserId(userId);
  }

  @Post('upsert_person')
  @ApiOperation({ summary: 'Insert or update person' })
  @ApiResponse({
    status: 200,
    description: 'Person inserted or updated successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Person not found',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async upsertPerson(@Body() body: PeopleDto) {
    return this.peopleService.upsertPerson(body);
  }

  @Get('person-by-person-type')
  @ApiOperation({ summary: 'Get person by person type' })
  @ApiResponse({
    status: 200,
    description: 'Person details',
  })
  async getPersonByPersonType(@Query('person_type') person_type: PersonType) {
    return this.peopleService.getPersonByPersonType(person_type);
  }

  @Get('get-partially-intrested-prospects')
  @ApiOperation({ summary: 'Get all prospects' })
  @ApiResponse({
    status: 200,
    description: 'List of prospects',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospects found',
  })
  async getPartiallyIntrestedProspects() {
    return this.peopleService.getPartiallyIntrestedPeople();
  }

  @Get('getTrialProspects')
  @ApiOperation({ summary: 'Get all prospects' })
  @ApiResponse({
    status: 200,
    description: 'List of prospects',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospects found',
  })
  async getTrialProspects() {
    return this.peopleService.getTrialProspects();
  }

  // getInFutureProspects
  @Get('getInFutureProspects')
  @ApiOperation({ summary: 'Get all prospects' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: 'Page size',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of prospects',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospects found',
  })
  async getInFutureProspects(
    @Query('page') page: number,
    @Query('pageSize') pageSize: number,
    @Query('search') search: string,
  ) {
    return this.peopleService.getInFutureProspects(page, pageSize, search);
  }
  // getConvertedProspects
  @Get('getConvertedProspects')
  @ApiOperation({ summary: 'Get all prospects' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: 'Page size',
    type: Number,
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'List of prospects',
  })
  @ApiResponse({
    status: 404,
    description: 'No prospects found',
  })
  async getConvertedProspects(
    @Query('page') page: number,
    @Query('pageSize') pageSize: number,
    @Query('search') search: string,
  ) {
    return this.peopleService.getConvertedProspects(page, pageSize, search);
  }

  @Get('getCandidateStatsBySource')
  @ApiOperation({ summary: 'Get candidate stats by source' })
  @ApiQuery({
    name: 'roleId',
    required: true,
    type: Number,
  })
  async candidateStatsBySource(@Query('roleId') roleId: number) {
    return this.peopleService.candidateStatsBySource(roleId);
  }

  @Get('getBdDashboard')
  async bdDashboard() {
    return this.peopleService.bdDashboard();
  }

  @Get('getCandidateBySearchString')
  @ApiOperation({
    summary: 'Search candidates using Boolean query with advanced filters',
  })
  @ApiQuery({
    name: 'q',
    required: false,
    description: 'Boolean string query for candidate search',
  })
  @ApiQuery({
    name: 'titles',
    required: false,
    description: 'Comma-separated list of job titles',
  })
  @ApiQuery({
    name: 'locations',
    required: false,
    description: 'Comma-separated list of locations',
  })
  @ApiQuery({
    name: 'skills',
    required: false,
    description: 'Comma-separated list of skills',
  })
  @ApiQuery({
    name: 'companies',
    required: false,
    description: 'Comma-separated list of companies',
  })
  @ApiQuery({
    name: 'industries',
    required: false,
    description: 'Comma-separated list of industries',
  })
  @ApiQuery({
    name: 'keywords',
    required: false,
    description: 'Comma-separated list of keywords',
  })
  @ApiQuery({
    name: 'seniority',
    required: false,
    description: 'Comma-separated list of seniority levels',
  })
  @ApiQuery({
    name: 'hideViewed',
    required: false,
    description: 'Hide previously viewed candidates',
    type: Boolean,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    description: 'Number of results per page',
    type: Number,
  })
  @ApiResponse({ status: 200, description: 'Search results', type: [People] })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async getCandidates(
    @Query('q') query?: string,
    @Query('titles') titles?: string,
    @Query('locations') locations?: string,
    @Query('skills') skills?: string,
    @Query('companies') companies?: string,
    @Query('industries') industries?: string,
    @Query('keywords') keywords?: string,
    @Query('seniority') seniority?: string,
    @Query('hideViewed') hideViewed?: boolean,
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 20,
  ) {
    try {
      const filters = {
        titles: titles ? titles.split(',').map((t) => t.trim()) : [],
        locations: locations ? locations.split(',').map((l) => l.trim()) : [],
        skills: skills ? skills.split(',').map((s) => s.trim()) : [],
        companies: companies ? companies.split(',').map((c) => c.trim()) : [],
        industries: industries
          ? industries.split(',').map((i) => i.trim())
          : [],
        keywords: keywords ? keywords.split(',').map((k) => k.trim()) : [],
        seniority: seniority ? seniority.split(',').map((s) => s.trim()) : [],
        hideViewed: hideViewed || false,
      };

      const result =
        await this.peopleService.searchCandidatesWithAdvancedFilters(
          query,
          filters,
          page,
          pageSize,
        );
      return result;
    } catch (error) {
      throw new BadRequestException(
        `Error processing search: ${error.message}`,
      );
    }
  }
}
