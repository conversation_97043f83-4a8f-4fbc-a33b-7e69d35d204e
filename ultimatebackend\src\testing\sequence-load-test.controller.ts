import { Controller, Post, Get, Delete, Body, Param, ParseIntPipe, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SequenceLoadTestService } from './sequence-load-test.service';

@Controller('testing/sequence-load')
@ApiTags('Sequence Load Testing')
export class SequenceLoadTestController {
  constructor(private readonly loadTestService: SequenceLoadTestService) {}

  @ApiOperation({ summary: 'Get existing candidates for load testing' })
  @ApiResponse({ status: 200, description: 'Existing candidates retrieved successfully' })
  @Post('candidates')
  async getExistingCandidates(
    @Body() body: { count: number; roleId: number }
  ): Promise<{ success: boolean; candidateIds: number[]; message: string }> {
    const { count, roleId } = body;

    if (count > 10000) {
      throw new Error('Maximum 10,000 candidates allowed per test');
    }

    const candidateIds = await this.loadTestService.getExistingCandidates(count, roleId);

    return {
      success: true,
      candidateIds,
      message: `Retrieved ${candidateIds.length} existing candidates for role ${roleId}`
    };
  }

  @ApiOperation({ summary: 'Get existing sequence for load testing' })
  @ApiResponse({ status: 200, description: 'Existing sequence retrieved successfully' })
  @Post('sequence')
  async getExistingSequence(
    @Body() body: { roleId: number }
  ): Promise<{ success: boolean; sequenceId: number; message: string }> {
    const { roleId } = body;

    const sequenceId = await this.loadTestService.getExistingSequence(roleId);

    return {
      success: true,
      sequenceId,
      message: `Using existing sequence ${sequenceId} for role ${roleId}`
    };
  }

  @ApiOperation({ summary: 'Execute load test with specified parameters' })
  @ApiResponse({ status: 200, description: 'Load test executed successfully' })
  @Post('execute')
  async executeLoadTest(
    @Body() body: {
      sequenceId: number;
      candidateIds: number[];
      batchSize?: number;
      delayBetweenBatches?: number;
      monitorProgress?: boolean;
    }
  ): Promise<{ success: boolean; message: string }> {
    const { sequenceId, candidateIds, ...options } = body;
    
    // Start load test (non-blocking)
    this.loadTestService.executeLoadTest(sequenceId, candidateIds, options)
      .catch(error => console.error('Load test failed:', error));
    
    // Start monitoring (non-blocking)
    if (options.monitorProgress !== false) {
      this.loadTestService.monitorExecution(sequenceId, candidateIds)
        .catch(error => console.error('Monitoring failed:', error));
    }
    
    return {
      success: true,
      message: `Load test started for ${candidateIds.length} candidates. Check logs for progress.`
    };
  }

  @ApiOperation({ summary: 'Run complete load test scenario' })
  @ApiResponse({ status: 200, description: 'Complete load test scenario executed' })
  @Post('scenario/complete')
  async runCompleteScenario(
    @Body() body: {
      roleId: number;
      candidateCount: number;
      batchSize?: number;
      delayBetweenBatches?: number;
      cleanupAfter?: boolean;
    }
  ): Promise<{ success: boolean; sequenceId: number; candidateIds: number[]; message: string }> {
    const { 
      roleId, 
      candidateCount, 
      batchSize = 100, 
      delayBetweenBatches = 5000,
      cleanupAfter = false 
    } = body;
    
    if (candidateCount > 10000) {
      throw new Error('Maximum 10,000 candidates allowed per test');
    }
    
    // Step 1: Get existing candidates
    const candidateIds = await this.loadTestService.getExistingCandidates(candidateCount, roleId);

    // Step 2: Get existing sequence
    const sequenceId = await this.loadTestService.getExistingSequence(roleId);
    
    // Step 3: Execute load test (non-blocking)
    this.loadTestService.executeLoadTest(sequenceId, candidateIds, {
      batchSize,
      delayBetweenBatches,
      monitorProgress: true
    }).then(() => {
      console.log('✅ Load test completed successfully');
      
      // Cleanup if requested
      if (cleanupAfter) {
        setTimeout(async () => {
          try {
            await this.loadTestService.cleanupTestData(candidateIds);
            console.log('✅ Test data cleanup completed');
          } catch (error) {
            console.error('❌ Cleanup failed:', error);
          }
        }, 60000); // Wait 1 minute before cleanup
      }
    }).catch(error => {
      console.error('❌ Load test failed:', error);
    });
    
    // Start monitoring (non-blocking)
    this.loadTestService.monitorExecution(sequenceId, candidateIds)
      .catch(error => console.error('Monitoring failed:', error));
    
    return {
      success: true,
      sequenceId,
      candidateIds,
      message: `Complete load test scenario started: ${candidateCount} candidates, sequence ${sequenceId}. Check logs for progress.`
    };
  }

  @ApiOperation({ summary: 'Clean up test data' })
  @ApiResponse({ status: 200, description: 'Test data cleaned up successfully' })
  @Delete('cleanup')
  async cleanupTestData(
    @Body() body: { candidateIds: number[] }
  ): Promise<{ success: boolean; message: string }> {
    const { candidateIds } = body;
    
    await this.loadTestService.cleanupTestData(candidateIds);
    
    return {
      success: true,
      message: `Cleaned up ${candidateIds.length} test candidates`
    };
  }

  @ApiOperation({ summary: 'Get predefined test scenarios' })
  @ApiResponse({ status: 200, description: 'Test scenarios retrieved successfully' })
  @Get('scenarios')
  async getTestScenarios(): Promise<any> {
    return {
      success: true,
      scenarios: [
        {
          name: 'Small Scale Test',
          description: 'Test with 50 candidates, good for initial testing',
          candidateCount: 50,
          batchSize: 10,
          delayBetweenBatches: 2000,
          estimatedDuration: '5 minutes'
        },
        {
          name: 'Medium Scale Test',
          description: 'Test with 500 candidates, moderate load testing',
          candidateCount: 500,
          batchSize: 50,
          delayBetweenBatches: 3000,
          estimatedDuration: '15 minutes'
        },
        {
          name: 'Large Scale Test',
          description: 'Test with 2000 candidates, heavy load testing',
          candidateCount: 2000,
          batchSize: 100,
          delayBetweenBatches: 5000,
          estimatedDuration: '30 minutes'
        },
        {
          name: 'Stress Test',
          description: 'Test with 5000 candidates, maximum load testing',
          candidateCount: 5000,
          batchSize: 200,
          delayBetweenBatches: 5000,
          estimatedDuration: '45 minutes'
        }
      ]
    };
  }

  @ApiOperation({ summary: 'Quick test with predefined scenario' })
  @ApiResponse({ status: 200, description: 'Quick test executed successfully' })
  @Post('quick-test/:scenario')
  async quickTest(
    @Param('scenario') scenario: string,
    @Body() body: { roleId: number; cleanupAfter?: boolean }
  ): Promise<any> {
    const { roleId, cleanupAfter = true } = body;
    
    const scenarios = {
      'small': { candidateCount: 50, batchSize: 10, delayBetweenBatches: 2000 },
      'medium': { candidateCount: 500, batchSize: 50, delayBetweenBatches: 3000 },
      'large': { candidateCount: 2000, batchSize: 100, delayBetweenBatches: 5000 },
      'stress': { candidateCount: 5000, batchSize: 200, delayBetweenBatches: 5000 }
    };
    
    const config = scenarios[scenario];
    if (!config) {
      throw new Error(`Unknown scenario: ${scenario}. Available: ${Object.keys(scenarios).join(', ')}`);
    }
    
    return await this.runCompleteScenario({
      roleId,
      ...config,
      cleanupAfter
    });
  }
}
