import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { CreateCompanyScrapperDto } from './dto/create-company_scrapper.dto';
import { UpdateCompanyScrapperDto } from './dto/update-company_scrapper.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CompanyScrapper } from './entities/company_scrapper.entity';

@Injectable()
export class CompanyScrapperService {
  constructor(
    @InjectRepository(CompanyScrapper)
    private companyScrapperRepo: Repository<CompanyScrapper>,
  ) {}

  async create(data: CreateCompanyScrapperDto) {
    if (data.date) {
      if (typeof data.date === 'string' && data.date.includes('T')) {
        data.date = data.date.split('T')[0];
      }
    }

    const companyScrapperData = {
      date: data.date,
      scrapper_profile_name: data.scrapper_profile_name,
      companies_extracted: data.companies_extracted,
    };

    try {
      const existingData = await this.companyScrapperRepo.findOne({
        where: {
          date: data.date,
          scrapper_profile_name: data.scrapper_profile_name,
        },
      });

      if (existingData) {
        await this.companyScrapperRepo.update(
          {
            date: data.date,
            scrapper_profile_name: data.scrapper_profile_name,
          },
          companyScrapperData,
        );
        return {
          message: `Report for date ${data.date} updated successfully`,
        };
      } else {
        await this.companyScrapperRepo.save(companyScrapperData);
        return {
          message: `Report for date ${data.date} created successfully`,
        };
      }
    } catch (error) {
      console.error('Error creating scrapper daily report:', error);
      throw new InternalServerErrorException(error.message);
    }
  }

  findAll() {
    return this.companyScrapperRepo.find();
  }

  findOne(id: number) {
    return `This action returns a #${id} companyScrapper`;
  }

  update(id: number, updateCompanyScrapperDto: UpdateCompanyScrapperDto) {
    return `This action updates a #${id} companyScrapper`;
  }

  remove(id: number) {
    return `This action removes a #${id} companyScrapper`;
  }
}
