import { <PERSON>, Get, Param, Query, BadRequestException } from '@nestjs/common';
import { CandidateSequenceStatusService } from './candidate-sequence-status.service';
import { CandidateSequenceStatus, SequenceStepStatus } from './candidate-sequence-status.entity';

@Controller('candidate-sequence-status')
export class CandidateSequenceStatusController {
  constructor(
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
  ) {}

  @Get('candidate/:candidateId/sequence/:sequenceId')
  async getCandidateSequenceStatus(
    @Param('candidateId') candidateId: number,
    @Param('sequenceId') sequenceId: number,
  ): Promise<CandidateSequenceStatus[]> {
    return await this.candidateSequenceStatusService.getCandidateSequenceStatus(
      candidateId,
      sequenceId,
    );
  }

  @Get('sequence/:sequenceId')
  async getCandidatesInSequenceWithNames(
    @Param('sequenceId') sequenceId: number,
  ): Promise<any[]> {
    return await this.candidateSequenceStatusService.getCandidatesInSequenceWithNames(sequenceId);
  }

  @Get('status/:status')
  async getStepsByStatus(
    @Param('status') status: string,
    @Query('limit') limit: number = 100,
  ): Promise<CandidateSequenceStatus[]> {
    // Convert to uppercase and validate against enum
    const statusUpper = status.toUpperCase();

    // Validate that the status is a valid enum value
    if (!Object.values(SequenceStepStatus).includes(statusUpper as SequenceStepStatus)) {
      throw new BadRequestException(`Invalid status: ${status}. Valid statuses are: ${Object.values(SequenceStepStatus).join(', ')}`);
    }

    const statusEnum = statusUpper as SequenceStepStatus;
    return await this.candidateSequenceStatusService.getStepsByStatus(
      statusEnum,
      limit,
    );
  }
}
