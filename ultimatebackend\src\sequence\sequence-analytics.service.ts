import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RoleSequence } from './sequence.entity';
import { CandidateSequenceStatus, SequenceStepStatus } from '../candidate-sequence-status/candidate-sequence-status.entity';
import { SequenceSteps } from '../sequence-steps/sequence_steps.entity';

@Injectable()
export class SequenceAnalyticsService {
  private readonly logger = new Logger(SequenceAnalyticsService.name);

  constructor(
    @InjectRepository(RoleSequence)
    private readonly sequenceRepository: Repository<RoleSequence>,
    @InjectRepository(CandidateSequenceStatus)
    private readonly candidateSequenceStatusRepository: Repository<CandidateSequenceStatus>,
    @InjectRepository(SequenceSteps)
    private readonly sequenceStepsRepository: Repository<SequenceSteps>,
  ) {}

  /**
   * Get comprehensive statistics for a specific sequence
   */
  async getSequenceStats(sequenceId: number) {
    try {
      // Get all candidate statuses for this sequence
      const candidateStatuses = await this.candidateSequenceStatusRepository.find({
        where: { sequenceId }
      });

      const total = candidateStatuses.length;
      const stats = {
        total,
        queued: candidateStatuses.filter(s => s.status === SequenceStepStatus.QUEUED).length,
        sent: candidateStatuses.filter(s => s.status === SequenceStepStatus.SENT).length,
        delivered: candidateStatuses.filter(s => s.status === SequenceStepStatus.DELIVERED).length,
        opened: candidateStatuses.filter(s => s.status === SequenceStepStatus.OPENED).length,
        replied: candidateStatuses.filter(s => s.status === SequenceStepStatus.REPLIED).length,
        failed: candidateStatuses.filter(s => s.status === SequenceStepStatus.FAILED).length,
        completed: candidateStatuses.filter(s =>
          [SequenceStepStatus.DELIVERED, SequenceStepStatus.OPENED, SequenceStepStatus.REPLIED].includes(s.status)
        ).length
      };

      // Calculate rates
      const rates = {
        responseRate: total > 0 ? (stats.replied / total * 100).toFixed(1) : 0,
        openRate: stats.sent > 0 ? (stats.opened / stats.sent * 100).toFixed(1) : 0,
        deliveryRate: stats.sent > 0 ? (stats.delivered / stats.sent * 100).toFixed(1) : 0,
        failureRate: total > 0 ? (stats.failed / total * 100).toFixed(1) : 0
      };

      return {
        ...stats,
        rates,
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error(`Error getting sequence stats for ${sequenceId}: ${error.message}`);
      return {
        total: 0,
        queued: 0,
        sent: 0,
        delivered: 0,
        opened: 0,
        replied: 0,
        failed: 0,
        completed: 0,
        rates: {
          responseRate: 0,
          openRate: 0,
          deliveryRate: 0,
          failureRate: 0
        },
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * Get step-by-step breakdown for a sequence
   */
  async getStepBreakdown(sequenceId: number) {
    try {
      const steps = await this.sequenceStepsRepository.find({
        where: { roleSequenceId: sequenceId },
        order: { order: 'ASC' }
      });

      const stepBreakdown = await Promise.all(
        steps.map(async (step) => {
          // For now, return mock data since the exact relationship isn't clear
          const stepStatuses = [];

          return {
            stepId: step.id,
            stepName: step.name,
            stepOrder: step.order,
            medium: step.medium,
            totalCandidates: stepStatuses.length,
            sent: stepStatuses.filter(s => s.status === SequenceStepStatus.SENT).length,
            delivered: stepStatuses.filter(s => s.status === SequenceStepStatus.DELIVERED).length,
            opened: stepStatuses.filter(s => s.status === SequenceStepStatus.OPENED).length,
            replied: stepStatuses.filter(s => s.status === SequenceStepStatus.REPLIED).length,
            failed: stepStatuses.filter(s => s.status === SequenceStepStatus.FAILED).length
          };
        })
      );

      return stepBreakdown;

    } catch (error) {
      this.logger.error(`Error getting step breakdown for ${sequenceId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Get follow-up effectiveness analysis
   */
  async getFollowUpEffectiveness(sequenceId: number) {
    try {
      const steps = await this.sequenceStepsRepository.find({
        where: { roleSequenceId: sequenceId },
        order: { order: 'ASC' }
      });

      const followUpSteps = steps.filter(step => step.parentStepId);
      const initialSteps = steps.filter(step => !step.parentStepId);

      const effectiveness = {
        initialStepResponses: 0,
        followUpResponses: 0,
        totalFollowUpsSent: 0,
        followUpConversionRate: 0
      };

      // Calculate initial step responses
      for (const step of initialSteps) {
        // Mock data for now
        const responses = 0;
        effectiveness.initialStepResponses += responses;
      }

      // Calculate follow-up effectiveness
      for (const step of followUpSteps) {
        // Mock data for now
        const sent = 0;
        const responses = 0;

        effectiveness.totalFollowUpsSent += sent;
        effectiveness.followUpResponses += responses;
      }

      effectiveness.followUpConversionRate = effectiveness.totalFollowUpsSent > 0
        ? parseFloat((effectiveness.followUpResponses / effectiveness.totalFollowUpsSent * 100).toFixed(1))
        : 0;

      return effectiveness;

    } catch (error) {
      this.logger.error(`Error getting follow-up effectiveness for ${sequenceId}: ${error.message}`);
      return {
        initialStepResponses: 0,
        followUpResponses: 0,
        totalFollowUpsSent: 0,
        followUpConversionRate: 0
      };
    }
  }

  /**
   * Get dashboard overview data
   */
  async getDashboardOverview() {
    try {
      const sequences = await this.sequenceRepository.find();
      
      const sequencesWithStats = await Promise.all(
        sequences.map(async (sequence) => {
          const stats = await this.getSequenceStats(sequence.id);
          return {
            ...sequence,
            stats
          };
        })
      );

      const summary = {
        totalSequences: sequences.length,
        activeSequences: sequences.filter(s => s.status === 'ACTIVE').length,
        totalCandidates: sequencesWithStats.reduce((sum, s) => sum + s.stats.total, 0),
        totalResponses: sequencesWithStats.reduce((sum, s) => sum + s.stats.replied, 0)
      };

      return {
        sequences: sequencesWithStats,
        summary,
        lastUpdated: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error(`Error getting dashboard overview: ${error.message}`);
      return {
        sequences: [],
        summary: {
          totalSequences: 0,
          activeSequences: 0,
          totalCandidates: 0,
          totalResponses: 0
        },
        lastUpdated: new Date().toISOString()
      };
    }
  }

  /**
   * Get recent activities across all sequences
   */
  async getRecentActivities(limit: number = 50) {
    try {
      // Return mock recent activities for now
      return Array.from({ length: Math.min(limit, 10) }, (_, i) => ({
        id: i + 1,
        candidateId: i + 1,
        sequenceId: 1,
        sequenceName: 'Test Sequence',
        status: 'SENT',
        timestamp: new Date(),
        stepId: 1
      }));

    } catch (error) {
      this.logger.error(`Error getting recent activities: ${error.message}`);
      return [];
    }
  }

  /**
   * Get performance trends over time
   */
  async getPerformanceTrends(sequenceId: number, startDate: Date, endDate: Date) {
    try {
      // This would typically involve more complex date-based queries
      // For now, return mock trend data
      const trends = {
        daily: [],
        weekly: [],
        monthly: []
      };

      // In a real implementation, you would query the database for time-series data
      // and calculate trends for sent, delivered, opened, replied metrics over time

      return trends;

    } catch (error) {
      this.logger.error(`Error getting performance trends for ${sequenceId}: ${error.message}`);
      return {
        daily: [],
        weekly: [],
        monthly: []
      };
    }
  }
}
