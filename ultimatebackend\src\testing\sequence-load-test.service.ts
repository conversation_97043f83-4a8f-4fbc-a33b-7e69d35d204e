import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { People } from '../people/people.entity';
import { PersonEmail } from '../email/email.entity';
import { PersonPhone } from '../phone/phone.entity';
import { RoleCandidate } from '../role-candidate/role-candidate.entity';
import { RoleSequence } from '../sequence/sequence.entity';
import { SequenceSteps } from '../sequence-steps/sequence_steps.entity';
import { SequenceService } from '../sequence/sequence.service';
import { CandidateSequenceStatusService } from '../candidate-sequence-status/candidate-sequence-status.service';

@Injectable()
export class SequenceLoadTestService {
  private readonly logger = new Logger(SequenceLoadTestService.name);

  constructor(
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
    @InjectRepository(PersonEmail)
    private readonly emailRepository: Repository<PersonEmail>,
    @InjectRepository(PersonPhone)
    private readonly phoneRepository: Repository<PersonPhone>,
    @InjectRepository(RoleCandidate)
    private readonly roleCandidateRepository: Repository<RoleCandidate>,
    @InjectRepository(RoleSequence)
    private readonly sequenceRepository: Repository<RoleSequence>,
    @InjectRepository(SequenceSteps)
    private readonly sequenceStepsRepository: Repository<SequenceSteps>,
    private readonly sequenceService: SequenceService,
    private readonly candidateSequenceStatusService: CandidateSequenceStatusService,
  ) {}

  /**
   * Create test candidates for load testing
   */
  async createTestCandidates(count: number, roleId: number): Promise<number[]> {
    this.logger.log(`Creating ${count} test candidates for role ${roleId}...`);
    
    const candidateIds: number[] = [];
    const batchSize = 50; // Process in batches to avoid memory issues
    
    for (let i = 0; i < count; i += batchSize) {
      const currentBatchSize = Math.min(batchSize, count - i);
      const batch = await this.createCandidateBatch(currentBatchSize, roleId, i);
      candidateIds.push(...batch);
      
      this.logger.log(`Created batch ${Math.floor(i / batchSize) + 1}: ${batch.length} candidates`);
    }
    
    this.logger.log(`✅ Created ${candidateIds.length} test candidates`);
    return candidateIds;
  }

  private async createCandidateBatch(batchSize: number, roleId: number, startIndex: number): Promise<number[]> {
    const candidateIds: number[] = [];
    
    for (let i = 0; i < batchSize; i++) {
      const index = startIndex + i;
      
      // Create person
      const person = this.peopleRepository.create({
        first_name: `TestCandidate${index}`,
        last_name: `LoadTest`,
        profile_url: `https://linkedin.com/in/testcandidate${index}`,
        created_at: new Date(),
        updated_at: new Date(),
      });
      
      const savedPerson = await this.peopleRepository.save(person);
      
      // Create email
      const email = this.emailRepository.create({
        email: `testcandidate${index}@loadtest.com`,
        email_type: 'BUSINESS',
        person_id: savedPerson.id,
        created_at: new Date(),
        updated_at: new Date(),
      });
      
      await this.emailRepository.save(email);
      
      // Create phone (for SMS/WhatsApp testing)
      const phone = this.phoneRepository.create({
        phone: `+1555000${String(index).padStart(4, '0')}`, // E.164 format
        phone_type: 'MOBILE',
        person_id: savedPerson.id,
        created_at: new Date(),
        updated_at: new Date(),
      });
      
      await this.phoneRepository.save(phone);
      
      // Create role candidate association
      const roleCandidate = this.roleCandidateRepository.create({
        role_id: roleId,
        candidate_id: savedPerson.id,
        created_at: new Date(),
        updated_at: new Date(),
      });
      
      await this.roleCandidateRepository.save(roleCandidate);
      candidateIds.push(savedPerson.id);
    }
    
    return candidateIds;
  }

  /**
   * Create a comprehensive test sequence with follow-ups
   */
  async createTestSequence(roleId: number): Promise<number> {
    this.logger.log(`Creating test sequence for role ${roleId}...`);
    
    // Create main sequence
    const sequence = this.sequenceRepository.create({
      name: `Load Test Sequence - Role ${roleId}`,
      description: 'Comprehensive test sequence with follow-ups for load testing',
      status: 'ACTIVE',
      userId: 1, // Assuming user ID 1 exists
      roleId: roleId,
      created_at: new Date(),
      updated_at: new Date(),
    });
    
    const savedSequence = await this.sequenceRepository.save(sequence);
    
    // Create sequence steps with follow-ups
    const steps = [
      {
        name: 'Initial Email Outreach',
        order: 0,
        type: 'OUTREACH',
        medium: 'EMAIL',
        templateId: 1,
        hasNoResponseFollowUp: true,
        noResponseDelayHours: 24,
      },
      {
        name: 'Follow-up Email',
        order: 1,
        type: 'NO_RESPONSE_FOLLOW_UP',
        medium: 'EMAIL',
        templateId: 2,
        parentStepId: null, // Will be set after first step is created
        followUpTrigger: 'DELAYED',
      },
      {
        name: 'SMS Follow-up',
        order: 2,
        type: 'NO_RESPONSE_FOLLOW_UP',
        medium: 'SMS',
        templateId: 3,
        parentStepId: null, // Will be set after first step is created
        followUpTrigger: 'DELAYED',
      },
      {
        name: 'WhatsApp Follow-up',
        order: 3,
        type: 'NO_RESPONSE_FOLLOW_UP',
        medium: 'WHATSAPP',
        templateId: 4,
        parentStepId: null, // Will be set after first step is created
        followUpTrigger: 'DELAYED',
      },
    ];
    
    let parentStepId: number | null = null;
    
    for (const stepData of steps) {
      const step = this.sequenceStepsRepository.create({
        ...stepData,
        roleSequenceId: savedSequence.id,
        parentStepId: stepData.type === 'NO_RESPONSE_FOLLOW_UP' ? parentStepId : null,
      });
      
      const savedStep = await this.sequenceStepsRepository.save(step);
      
      // Set parent step ID for follow-up steps
      if (stepData.type === 'OUTREACH') {
        parentStepId = savedStep.id;
      }
    }
    
    this.logger.log(`✅ Created test sequence ${savedSequence.id} with ${steps.length} steps`);
    return savedSequence.id;
  }

  /**
   * Execute sequence with load testing
   */
  async executeLoadTest(
    sequenceId: number,
    candidateIds: number[],
    options: {
      batchSize?: number;
      delayBetweenBatches?: number;
      monitorProgress?: boolean;
    } = {}
  ): Promise<void> {
    const { batchSize = 100, delayBetweenBatches = 5000, monitorProgress = true } = options;
    
    this.logger.log(`Starting load test: ${candidateIds.length} candidates, batch size: ${batchSize}`);
    
    const totalBatches = Math.ceil(candidateIds.length / batchSize);
    
    for (let i = 0; i < candidateIds.length; i += batchSize) {
      const batch = candidateIds.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      
      this.logger.log(`Processing batch ${batchNumber}/${totalBatches}: ${batch.length} candidates`);
      
      try {
        // Execute sequence for this batch
        await this.sequenceService.startSequence(sequenceId, batch);
        
        this.logger.log(`✅ Batch ${batchNumber} queued successfully`);
        
        if (monitorProgress) {
          await this.logQueueStats();
        }
        
        // Delay between batches to prevent overwhelming the system
        if (i + batchSize < candidateIds.length && delayBetweenBatches > 0) {
          this.logger.log(`Waiting ${delayBetweenBatches}ms before next batch...`);
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
        }
        
      } catch (error) {
        this.logger.error(`❌ Failed to process batch ${batchNumber}: ${error.message}`);
        throw error;
      }
    }
    
    this.logger.log(`🎉 Load test completed: ${candidateIds.length} candidates processed`);
  }

  /**
   * Monitor sequence execution progress
   */
  async monitorExecution(sequenceId: number, candidateIds: number[]): Promise<void> {
    this.logger.log(`Monitoring execution for sequence ${sequenceId}...`);
    
    const monitoringInterval = setInterval(async () => {
      try {
        const stats = await this.getExecutionStats(sequenceId, candidateIds);
        this.logger.log(`📊 Execution Stats: ${JSON.stringify(stats, null, 2)}`);
        
        // Stop monitoring when all candidates are processed
        if (stats.completed + stats.failed >= candidateIds.length) {
          clearInterval(monitoringInterval);
          this.logger.log(`🏁 Monitoring completed. Final stats: ${JSON.stringify(stats, null, 2)}`);
        }
        
      } catch (error) {
        this.logger.error(`Error monitoring execution: ${error.message}`);
      }
    }, 10000); // Check every 10 seconds
  }

  private async getExecutionStats(sequenceId: number, candidateIds: number[]) {
    const stats = await this.candidateSequenceStatusService.getSequenceStats(sequenceId);
    
    return {
      total: candidateIds.length,
      queued: stats.filter(s => s.status === 'QUEUED').length,
      sent: stats.filter(s => s.status === 'SENT').length,
      delivered: stats.filter(s => s.status === 'DELIVERED').length,
      opened: stats.filter(s => s.status === 'OPENED').length,
      replied: stats.filter(s => s.status === 'REPLIED').length,
      failed: stats.filter(s => s.status === 'FAILED').length,
      completed: stats.filter(s => ['DELIVERED', 'OPENED', 'REPLIED'].includes(s.status)).length,
    };
  }

  private async logQueueStats(): Promise<void> {
    // This would integrate with your queue service to get real-time stats
    // Implementation depends on your queue monitoring setup
    this.logger.log('📈 Queue stats logged (implement queue monitoring integration)');
  }

  /**
   * Clean up test data
   */
  async cleanupTestData(candidateIds: number[]): Promise<void> {
    this.logger.log(`Cleaning up ${candidateIds.length} test candidates...`);
    
    // Delete in reverse order to handle foreign key constraints
    await this.roleCandidateRepository.delete({ candidate_id: candidateIds });
    await this.emailRepository.delete({ person_id: candidateIds });
    await this.phoneRepository.delete({ person_id: candidateIds });
    await this.peopleRepository.delete(candidateIds);
    
    this.logger.log(`✅ Cleanup completed`);
  }
}
