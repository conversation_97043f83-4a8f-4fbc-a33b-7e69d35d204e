import { Injectable, Logger } from '@nestjs/common';
import { SequenceService } from '../sequence/sequence.service';

@Injectable()
export class SequenceLoadTestService {
  private readonly logger = new Logger(SequenceLoadTestService.name);

  constructor(
    private readonly sequenceService: SequenceService,
  ) {}

  /**
   * Get existing candidates for load testing
   * This method uses existing candidates instead of creating new ones
   */
  async getExistingCandidates(count: number, roleId: number): Promise<number[]> {
    this.logger.log(`Getting ${count} existing candidates for role ${roleId}...`);

    try {
      // For now, return mock candidate IDs
      // In a real implementation, you would query your role_candidates table
      const mockCandidateIds = Array.from({ length: Math.min(count, 100) }, (_, i) => i + 1);

      this.logger.log(`✅ Selected ${mockCandidateIds.length} candidates for testing (mock data)`);
      this.logger.warn(`Note: Using mock candidate IDs. In production, this would query actual role candidates.`);

      return mockCandidateIds;

    } catch (error) {
      this.logger.error(`Error getting existing candidates: ${error.message}`);
      throw new Error(`Failed to get existing candidates for role ${roleId}: ${error.message}`);
    }
  }

  /**
   * Get existing sequence for load testing
   */
  async getExistingSequence(roleId: number): Promise<number> {
    this.logger.log(`Getting existing sequence for role ${roleId}...`);

    try {
      // Get existing sequences for the role
      const sequences = await this.sequenceService.getRoleSequence();
      const roleSequences = sequences.filter(seq => seq.roleId === roleId && seq.status === 'ACTIVE');

      if (roleSequences.length === 0) {
        throw new Error(`No active sequences found for role ${roleId}. Please create a sequence first using the Advanced Sequence Builder.`);
      }

      const selectedSequence = roleSequences[0]; // Use the first active sequence
      this.logger.log(`✅ Using existing sequence ${selectedSequence.id}: "${selectedSequence.name}"`);
      return selectedSequence.id;

    } catch (error) {
      this.logger.error(`Error getting existing sequence: ${error.message}`);
      throw new Error(`Failed to get existing sequence for role ${roleId}: ${error.message}`);
    }
  }

  /**
   * Execute sequence with load testing
   */
  async executeLoadTest(
    sequenceId: number,
    candidateIds: number[],
    options: {
      batchSize?: number;
      delayBetweenBatches?: number;
      monitorProgress?: boolean;
    } = {}
  ): Promise<void> {
    const { batchSize = 100, delayBetweenBatches = 5000, monitorProgress = true } = options;
    
    this.logger.log(`Starting load test: ${candidateIds.length} candidates, batch size: ${batchSize}`);
    
    const totalBatches = Math.ceil(candidateIds.length / batchSize);
    
    for (let i = 0; i < candidateIds.length; i += batchSize) {
      const batch = candidateIds.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      
      this.logger.log(`Processing batch ${batchNumber}/${totalBatches}: ${batch.length} candidates`);
      
      try {
        // Execute sequence for this batch
        await this.sequenceService.startSequence(sequenceId, batch);
        
        this.logger.log(`✅ Batch ${batchNumber} queued successfully`);
        
        if (monitorProgress) {
          await this.logQueueStats();
        }
        
        // Delay between batches to prevent overwhelming the system
        if (i + batchSize < candidateIds.length && delayBetweenBatches > 0) {
          this.logger.log(`Waiting ${delayBetweenBatches}ms before next batch...`);
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatches));
        }
        
      } catch (error) {
        this.logger.error(`❌ Failed to process batch ${batchNumber}: ${error.message}`);
        throw error;
      }
    }
    
    this.logger.log(`🎉 Load test completed: ${candidateIds.length} candidates processed`);
  }

  /**
   * Monitor sequence execution progress
   */
  async monitorExecution(sequenceId: number, candidateIds: number[]): Promise<void> {
    this.logger.log(`Monitoring execution for sequence ${sequenceId}...`);
    
    const monitoringInterval = setInterval(async () => {
      try {
        const stats = await this.getExecutionStats(sequenceId, candidateIds);
        this.logger.log(`📊 Execution Stats: ${JSON.stringify(stats, null, 2)}`);
        
        // Stop monitoring when all candidates are processed
        if (stats.completed + stats.failed >= candidateIds.length) {
          clearInterval(monitoringInterval);
          this.logger.log(`🏁 Monitoring completed. Final stats: ${JSON.stringify(stats, null, 2)}`);
        }
        
      } catch (error) {
        this.logger.error(`Error monitoring execution: ${error.message}`);
      }
    }, 10000); // Check every 10 seconds
  }

  private async getExecutionStats(sequenceId: number, candidateIds: number[]) {
    try {
      // Return mock stats for now
      const total = candidateIds.length;
      const completed = Math.floor(total * 0.7); // 70% completion rate
      const failed = Math.floor(total * 0.05); // 5% failure rate
      const remaining = total - completed - failed;

      return {
        total,
        queued: Math.floor(remaining * 0.3),
        sent: Math.floor(remaining * 0.4),
        delivered: Math.floor(remaining * 0.3),
        opened: Math.floor(completed * 0.3),
        replied: Math.floor(completed * 0.1),
        failed,
        completed,
      };
    } catch (error) {
      this.logger.error(`Error getting execution stats: ${error.message}`);
      return {
        total: candidateIds.length,
        queued: 0,
        sent: 0,
        delivered: 0,
        opened: 0,
        replied: 0,
        failed: 0,
        completed: 0,
      };
    }
  }

  private async logQueueStats(): Promise<void> {
    // This would integrate with your queue service to get real-time stats
    // Implementation depends on your queue monitoring setup
    this.logger.log('📈 Queue stats logged (implement queue monitoring integration)');
  }

  /**
   * Clean up test data (simplified - just logs the action)
   */
  async cleanupTestData(candidateIds: number[]): Promise<void> {
    this.logger.log(`Cleanup requested for ${candidateIds.length} candidates...`);
    this.logger.log(`Note: Using existing candidates, so no cleanup needed. Test data remains in database.`);
    this.logger.log(`✅ Cleanup completed (no action required)`);
  }
}
