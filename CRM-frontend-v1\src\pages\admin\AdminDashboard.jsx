import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch } from 'react-redux';
import { setUserDesignation, setUserRole } from 'redux/slices/userSlice';

function AdminDashboard(props) {
  const dispatch = useDispatch();
  const loginToClientPortal = () => {
    dispatch(setUserDesignation('CLIENT'));
    dispatch(setUserRole('CLIENT'));
    window.open('/c-dashboard', '_blank');
  }

  return (
    <div>
      <Helmet>
        <title>Admin Dashboard</title>
      </Helmet>
      <h1>WELCOME ADMIN</h1>
      <div>
        <span style={{ color: '#1a84de', textDecoration: 'underline', cursor: 'pointer', fontSize: '16px' }}>Log in to Client Portal</span>
      </div>
    </div>
  );
}

export default AdminDashboard;