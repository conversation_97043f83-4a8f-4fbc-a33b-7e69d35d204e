import { Icon } from '@iconify/react';

// ==============================|| MENU ITEMS - DASHBOARD ||============================== //

const Client_dashboard = {
    id: 'group-dashboard',
    title: '',
    type: 'group',
    children: [
        {
            id: 'dashboard',
            title: 'Dashboard',
            type: 'item',
            url: '/c-dashboard',
            icon: () => <Icon icon="lsicon:home-filled" width="20" height="20" />,
            breadcrumbs: false
        }
    ]
};

export default Client_dashboard;
