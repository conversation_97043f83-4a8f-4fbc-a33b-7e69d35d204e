import React, { useState } from 'react';
import { Card, Button, Space, message, Alert, Descriptions, Tag, InputNumber, Select, Divider } from 'antd';
import { ClockCircleOutlined, SendOutlined, CheckCircleOutlined, CloseCircleOutlined, RocketOutlined, Bar<PERSON>hartOutlined } from '@ant-design/icons';
import * as sequenceAPI from '../api/sequenceExecution';
import { Post } from '../api/api';

const { Option } = Select;

const TestFollowUpFunctionality = () => {
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState([]);
  const [loadTestConfig, setLoadTestConfig] = useState({
    roleId: 1,
    candidateCount: 50,
    scenario: 'small'
  });

  const addTestResult = (test, status, message) => {
    setTestResults(prev => [...prev, { test, status, message, timestamp: new Date().toLocaleTimeString() }]);
  };

  const testCreateFollowUpSteps = async () => {
    try {
      setLoading(true);
      addTestResult('Create Follow-up Steps', 'running', 'Creating follow-up steps for test step...');

      const followUpStepsData = [
        {
          name: 'Follow-up Email',
          type: 'NO_RESPONSE_FOLLOW_UP',
          medium: 'EMAIL',
          templateId: 1,
          order: 1,
          noResponseDelayHours: 24
        },
        {
          name: 'Follow-up SMS',
          type: 'NO_RESPONSE_FOLLOW_UP',
          medium: 'SMS',
          templateId: 2,
          order: 2,
          noResponseDelayHours: 48
        }
      ];

      // Test with a mock step ID (you may need to adjust this)
      const result = await sequenceAPI.createFollowUpSteps(1, followUpStepsData);
      
      addTestResult('Create Follow-up Steps', 'success', `Created ${followUpStepsData.length} follow-up steps successfully`);
      console.log('Follow-up creation result:', result);

    } catch (error) {
      addTestResult('Create Follow-up Steps', 'error', `Failed: ${error.message}`);
      console.error('Follow-up creation error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testGetFollowUpSteps = async () => {
    try {
      setLoading(true);
      addTestResult('Get Follow-up Steps', 'running', 'Fetching follow-up steps...');

      const result = await sequenceAPI.getFollowUpSteps(1);
      
      addTestResult('Get Follow-up Steps', 'success', `Retrieved ${result.followUpSteps?.length || 0} follow-up steps`);
      console.log('Follow-up steps:', result);

    } catch (error) {
      addTestResult('Get Follow-up Steps', 'error', `Failed: ${error.message}`);
      console.error('Get follow-up steps error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testManualTrigger = async () => {
    try {
      setLoading(true);
      addTestResult('Manual Trigger', 'running', 'Triggering manual follow-up...');

      const result = await sequenceAPI.triggerManualFollowUp(1, 1);
      
      addTestResult('Manual Trigger', 'success', 'Manual follow-up triggered successfully');
      console.log('Manual trigger result:', result);

    } catch (error) {
      addTestResult('Manual Trigger', 'error', `Failed: ${error.message}`);
      console.error('Manual trigger error:', error);
    } finally {
      setLoading(false);
    }
  };

  const testResponseCheck = async () => {
    try {
      setLoading(true);
      addTestResult('Response Check', 'running', 'Checking candidate response status...');

      const result = await sequenceAPI.hasRespondedToSequence(1, 1);
      
      addTestResult('Response Check', 'success', `Candidate has ${result.hasResponded ? '' : 'not '}responded`);
      console.log('Response check result:', result);

    } catch (error) {
      addTestResult('Response Check', 'error', `Failed: ${error.message}`);
      console.error('Response check error:', error);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  // Load Testing Functions
  const runLoadTest = async (scenario) => {
    try {
      setLoading(true);
      addTestResult('Load Test', 'running', `Starting ${scenario} load test...`);

      const response = await Post(`http://localhost:5001/api/testing/sequence-load/quick-test/${scenario}`, {
        roleId: loadTestConfig.roleId,
        cleanupAfter: true
      });

      addTestResult('Load Test', 'success', `${scenario} load test started successfully. Check backend logs for progress.`);
      console.log('Load test response:', response);

    } catch (error) {
      addTestResult('Load Test', 'error', `Load test failed: ${error.message}`);
      console.error('Load test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const runCustomLoadTest = async () => {
    try {
      setLoading(true);
      addTestResult('Custom Load Test', 'running', `Starting custom load test with ${loadTestConfig.candidateCount} candidates...`);

      const response = await Post('http://localhost:5001/api/testing/sequence-load/scenario/complete', {
        roleId: loadTestConfig.roleId,
        candidateCount: loadTestConfig.candidateCount,
        batchSize: Math.min(100, Math.ceil(loadTestConfig.candidateCount / 10)),
        delayBetweenBatches: 3000,
        cleanupAfter: true
      });

      addTestResult('Custom Load Test', 'success', `Custom load test started with ${loadTestConfig.candidateCount} candidates. Check backend logs for progress.`);
      console.log('Custom load test response:', response);

    } catch (error) {
      addTestResult('Custom Load Test', 'error', `Custom load test failed: ${error.message}`);
      console.error('Custom load test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getQueueStats = async () => {
    try {
      setLoading(true);
      addTestResult('Queue Stats', 'running', 'Fetching queue statistics...');

      const response = await sequenceAPI.getQueueStats();

      addTestResult('Queue Stats', 'success', `Queue stats retrieved. Check console for details.`);
      console.log('Queue stats:', response);

    } catch (error) {
      addTestResult('Queue Stats', 'error', `Failed to get queue stats: ${error.message}`);
      console.error('Queue stats error:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'running': return 'blue';
      default: return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'success': return <CheckCircleOutlined />;
      case 'error': return <CloseCircleOutlined />;
      case 'running': return <ClockCircleOutlined />;
      default: return null;
    }
  };

  return (
    <div style={{ padding: 24, maxWidth: 1200, margin: '0 auto' }}>
      <Card title="Follow-up Functionality Test Suite" style={{ marginBottom: 24 }}>
        <Alert
          message="Follow-up Testing"
          description="This page tests the follow-up functionality for sequence steps. Make sure your backend is running and the database is properly configured."
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Space wrap>
          <Button 
            type="primary" 
            icon={<SendOutlined />}
            onClick={testCreateFollowUpSteps}
            loading={loading}
          >
            Test Create Follow-up Steps
          </Button>
          
          <Button 
            onClick={testGetFollowUpSteps}
            loading={loading}
          >
            Test Get Follow-up Steps
          </Button>
          
          <Button 
            onClick={testManualTrigger}
            loading={loading}
          >
            Test Manual Trigger
          </Button>
          
          <Button 
            onClick={testResponseCheck}
            loading={loading}
          >
            Test Response Check
          </Button>
          
          <Button 
            onClick={clearResults}
            type="dashed"
          >
            Clear Results
          </Button>
        </Space>
      </Card>

      {testResults.length > 0 && (
        <Card title="Test Results" style={{ marginBottom: 24 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {testResults.map((result, index) => (
              <Card 
                key={index} 
                size="small"
                style={{ 
                  borderLeft: `4px solid ${getStatusColor(result.status) === 'green' ? '#52c41a' : 
                                           getStatusColor(result.status) === 'red' ? '#ff4d4f' : 
                                           getStatusColor(result.status) === 'blue' ? '#1890ff' : '#d9d9d9'}`
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Space>
                    <Tag color={getStatusColor(result.status)} icon={getStatusIcon(result.status)}>
                      {result.status.toUpperCase()}
                    </Tag>
                    <strong>{result.test}</strong>
                    <span>{result.message}</span>
                  </Space>
                  <span style={{ color: '#666', fontSize: '12px' }}>{result.timestamp}</span>
                </div>
              </Card>
            ))}
          </Space>
        </Card>
      )}

      {/* Load Testing Section */}
      <Card title="Load Testing - Test with Robust Number of Candidates" style={{ marginBottom: 24 }}>
        <Alert
          message="Load Testing Capabilities"
          description="Test your sequence execution with hundreds or thousands of candidates to verify system performance and reliability."
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* Configuration */}
        <Card size="small" title="Load Test Configuration" style={{ marginBottom: 16 }}>
          <Space wrap>
            <div>
              <label style={{ display: 'block', marginBottom: 4 }}>Role ID:</label>
              <InputNumber
                min={1}
                value={loadTestConfig.roleId}
                onChange={(value) => setLoadTestConfig(prev => ({ ...prev, roleId: value }))}
                style={{ width: 100 }}
              />
            </div>
            <div>
              <label style={{ display: 'block', marginBottom: 4 }}>Custom Candidate Count:</label>
              <InputNumber
                min={10}
                max={10000}
                value={loadTestConfig.candidateCount}
                onChange={(value) => setLoadTestConfig(prev => ({ ...prev, candidateCount: value }))}
                style={{ width: 150 }}
              />
            </div>
          </Space>
        </Card>

        {/* Quick Test Scenarios */}
        <Card size="small" title="Quick Test Scenarios" style={{ marginBottom: 16 }}>
          <Space wrap>
            <Button
              type="primary"
              icon={<RocketOutlined />}
              onClick={() => runLoadTest('small')}
              loading={loading}
            >
              Small Test (50 candidates)
            </Button>

            <Button
              onClick={() => runLoadTest('medium')}
              loading={loading}
            >
              Medium Test (500 candidates)
            </Button>

            <Button
              onClick={() => runLoadTest('large')}
              loading={loading}
            >
              Large Test (2000 candidates)
            </Button>

            <Button
              danger
              onClick={() => runLoadTest('stress')}
              loading={loading}
            >
              Stress Test (5000 candidates)
            </Button>
          </Space>
        </Card>

        {/* Custom Load Test */}
        <Card size="small" title="Custom Load Test" style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={runCustomLoadTest}
              loading={loading}
            >
              Run Custom Load Test ({loadTestConfig.candidateCount} candidates)
            </Button>

            <Button
              icon={<BarChartOutlined />}
              onClick={getQueueStats}
              loading={loading}
            >
              Get Queue Statistics
            </Button>
          </Space>
        </Card>

        <Alert
          message="Load Testing Notes"
          description={
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              <li><strong>Small Test (50):</strong> Good for initial functionality testing (~5 minutes)</li>
              <li><strong>Medium Test (500):</strong> Moderate load testing (~15 minutes)</li>
              <li><strong>Large Test (2000):</strong> Heavy load testing (~30 minutes)</li>
              <li><strong>Stress Test (5000):</strong> Maximum capacity testing (~45 minutes)</li>
              <li><strong>Custom Test:</strong> Configure your own candidate count</li>
              <li><strong>Monitoring:</strong> Check backend console logs for real-time progress</li>
              <li><strong>Cleanup:</strong> Test data is automatically cleaned up after completion</li>
            </ul>
          }
          type="info"
          showIcon
        />
      </Card>

      <Divider />

      <Card title="Follow-up Configuration Guide">
        <Descriptions column={1} bordered>
          <Descriptions.Item label="Step 1">
            Create a sequence using the Advanced Sequence Builder
          </Descriptions.Item>
          <Descriptions.Item label="Step 2">
            Click "Edit" on any sequence step to open the template editor
          </Descriptions.Item>
          <Descriptions.Item label="Step 3">
            Expand the "Follow-up Configuration" section
          </Descriptions.Item>
          <Descriptions.Item label="Step 4">
            Enable follow-up and configure your follow-up steps
          </Descriptions.Item>
          <Descriptions.Item label="Step 5">
            Save the sequence - follow-ups will be created automatically
          </Descriptions.Item>
          <Descriptions.Item label="Step 6">
            The system will automatically trigger follow-ups for non-responders
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};

export default TestFollowUpFunctionality;
