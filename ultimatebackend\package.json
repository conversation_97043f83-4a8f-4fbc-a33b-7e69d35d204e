{"name": "ultimate", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nodemon --exec ts-node -r tsconfig-paths/register src/main.ts", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@aws-sdk/client-s3": "^3.758.0", "@aws-sdk/client-ses": "^3.758.0", "@aws-sdk/lib-storage": "^3.758.0", "@aws-sdk/s3-request-presigner": "^3.758.0", "@nestjs/bull": "^10.2.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.18", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.18", "all-the-cities": "^3.1.0", "archiver": "^7.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "bull": "^4.16.5", "bullmq": "^5.53.2", "cache-manager": "^6.4.1", "class-transformer": "^0.5.1", "cloudinary": "^2.2.0", "country-state-city": "^3.2.1", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "ioredis": "^5.6.1", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^6.10.0", "pdfkit": "^0.15.0", "pg": "^8.12.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "stripe": "^16.6.0", "swagger-ui-express": "^5.0.1", "twilio": "^5.6.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.2", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/socket.io": "^3.0.1", "@types/stripe": "^8.0.417", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "bcrypt": "^5.1.1", "class-validator": "^0.14.1", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "express-rate-limit": "^7.3.1", "jest": "^29.5.0", "nodemon": "^3.1.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}