import { Controller, Post, Put, Delete, Get } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { SectorService } from './sector.service';

@Controller('sector')
@ApiTags('sector')
export class SectorController {
  constructor(private readonly sectorService: SectorService) {}

  @Post()
  @ApiOperation({ summary: 'Create Sector' })
  async createSector(name: string) {
    return this.sectorService.createSector(name);
  }

  @Put('update')
  @ApiOperation({ summary: 'Update Sector' })
  async updateSector(id: number, name: string) {
    return this.sectorService.updateSector(id, name);
  }

  @Delete('delete/:id')
  @ApiOperation({ summary: 'Delete Sector' })
  async deleteSector(id: number) {
    return this.sectorService.deleteSector(id);
  }

  @Get('getAllSectors')
  async getAllSectors() {
    return this.sectorService.findAll();
  }
}
