import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { SequenceService } from './sequence.service';
import { FollowUpService } from './follow-up.service';
import { RoleSequenceDto, UpdateRoleSequenceDto } from './dto/sequence..dto';
import { RoleSequence } from './sequence.entity';

@Controller('sequence')
@ApiTags('Sequence')
export class SequenceController {
  constructor(
    private readonly sequenceService: SequenceService,
    private readonly followUpService: FollowUpService,
  ) {}

  @ApiOperation({ summary: 'Create a new sequence' })
  @Post('create')
  async createSequence(
    @Body() sequenceDto: RoleSequenceDto,
  ): Promise<RoleSequence> {
    return this.sequenceService.createRoleSequence(sequenceDto);
  }

  @ApiOperation({ summary: 'Get all sequences' })
  @Get('get-all')
  async getAllSequences(): Promise<RoleSequence[]> {
    return this.sequenceService.getRoleSequence();
  }

  @ApiOperation({ summary: 'Get a sequence by ID' })
  @Get(':id')
  async getSequenceById(@Param('id', ParseIntPipe) id: number): Promise<RoleSequence> {
    return this.sequenceService.getRoleSequenceById(id);
  }

  @ApiOperation({ summary: 'Update a sequence by ID' })
  @Put(':id')
  async updateSequence(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSequenceDto: UpdateRoleSequenceDto,
  ): Promise<RoleSequence> {
    return this.sequenceService.updateRoleSequence(id, updateSequenceDto);
  }

  @ApiOperation({ summary: 'Delete a sequence by ID' })
  @Delete(':id')
  async deleteSequence(@Param('id', ParseIntPipe) id: number): Promise<void> {
    return this.sequenceService.deleteRoleSequence(id);
  }

  @ApiOperation({ summary: 'Start a sequence for candidates' })
  @Post(':id/start')
  async startSequence(
    @Param('id', ParseIntPipe) sequenceId: number,
    @Body() body: { candidateIds: number[] },
  ): Promise<{ success: boolean; message: string }> {
    await this.sequenceService.startSequence(sequenceId, body.candidateIds);
    return {
      success: true,
      message: `Sequence ${sequenceId} started for ${body.candidateIds.length} candidates`,
    };
  }

  @ApiOperation({ summary: 'Get sequence with steps' })
  @Get(':id/with-steps')
  async getSequenceWithSteps(@Param('id', ParseIntPipe) sequenceId: number): Promise<RoleSequence> {
    return this.sequenceService.getSequenceWithSteps(sequenceId);
  }

  @ApiOperation({ summary: 'Start sequence with test candidates' })
  @Post(':id/start-with-test-candidates')
  async startSequenceWithTestCandidates(@Param('id', ParseIntPipe) sequenceId: number): Promise<{ success: boolean; message: string }> {
    await this.sequenceService.startSequenceWithTestCandidates(sequenceId);
    return {
      success: true,
      message: `Sequence ${sequenceId} started with test candidates`,
    };
  }

  @ApiOperation({ summary: 'Start sequence with role candidates' })
  @Post(':id/start-with-role-candidates')
  async startSequenceWithRoleCandidates(
    @Param('id', ParseIntPipe) sequenceId: number,
    @Body() body: { roleId: number; limit?: number },
  ): Promise<{ success: boolean; message: string }> {
    await this.sequenceService.startSequenceWithRoleCandidates(
      sequenceId,
      body.roleId,
      body.limit || 10
    );
    return {
      success: true,
      message: `Sequence ${sequenceId} started with role candidates for role ${body.roleId}`,
    };
  }

  @ApiOperation({ summary: 'Get sequence execution statistics' })
  @Get(':id/stats')
  async getSequenceStats(@Param('id', ParseIntPipe) sequenceId: number): Promise<any> {
    return this.sequenceService.getSequenceStats(sequenceId);
  }

  @ApiOperation({ summary: 'Debug sequence and candidate data' })
  @Get(':id/debug')
  async debugSequence(@Param('id', ParseIntPipe) sequenceId: number): Promise<any> {
    return this.sequenceService.debugSequenceData(sequenceId);
  }

  @ApiOperation({ summary: 'Check database health for sequences' })
  @Get('debug/health')
  async checkDatabaseHealth(): Promise<any> {
    return this.sequenceService.checkDatabaseHealth();
  }

  @ApiOperation({ summary: 'Create follow-up steps for a sequence step' })
  @Post('steps/:stepId/follow-up')
  async createFollowUpSteps(
    @Param('stepId', ParseIntPipe) stepId: number,
    @Body() followUpStepsData: Array<{
      name: string;
      type: string;
      medium: string;
      templateId?: number;
      order: number;
      noResponseDelayHours?: number;
    }>
  ): Promise<any> {
    const followUpSteps = await this.followUpService.createFollowUpSteps(stepId, followUpStepsData);
    return {
      success: true,
      message: `Created ${followUpSteps.length} follow-up steps for step ${stepId}`,
      followUpSteps
    };
  }

  @ApiOperation({ summary: 'Get follow-up steps for a sequence step' })
  @Get('steps/:stepId/follow-up')
  async getFollowUpSteps(@Param('stepId', ParseIntPipe) stepId: number): Promise<any> {
    const followUpSteps = await this.followUpService.getFollowUpSteps(stepId);
    return {
      success: true,
      followUpSteps
    };
  }

  @ApiOperation({ summary: 'Manually trigger follow-up for a candidate step' })
  @Post('candidate/:candidateId/step/:stepId/trigger-follow-up')
  async triggerManualFollowUp(
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @Param('stepId', ParseIntPipe) stepId: number,
  ): Promise<any> {
    await this.followUpService.triggerManualFollowUp(candidateId, stepId);
    return {
      success: true,
      message: `Follow-up triggered for candidate ${candidateId}, step ${stepId}`
    };
  }

  @ApiOperation({ summary: 'Check if candidate has responded to sequence' })
  @Get('candidate/:candidateId/sequence/:sequenceId/has-responded')
  async hasRespondedToSequence(
    @Param('candidateId', ParseIntPipe) candidateId: number,
    @Param('sequenceId', ParseIntPipe) sequenceId: number,
  ): Promise<any> {
    const hasResponded = await this.followUpService.hasRespondedToSequence(candidateId, sequenceId);
    return {
      success: true,
      hasResponded
    };
  }
}
