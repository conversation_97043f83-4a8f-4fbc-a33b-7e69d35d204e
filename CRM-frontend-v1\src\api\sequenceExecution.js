import { Get as GetAction, Post as PostAction, Put as PutAction, Delete as DeleteAction } from '../actions/API/apiActions';
import { API_URLS } from '../constants/apiUrls';

// Wrapper functions to handle the API action parameter order
const Get = (url) => {
  return new Promise((resolve, reject) => {
    GetAction(null, url, resolve, reject);
  });
};

const Post = (url, data) => {
  return new Promise((resolve, reject) => {
    PostAction(data, url, resolve, reject);
  });
};

const Put = (url, data) => {
  return new Promise((resolve, reject) => {
    PutAction(data, url, resolve, reject);
  });
};

const Delete = (url, data = null) => {
  return new Promise((resolve, reject) => {
    DeleteAction(data, url, () => resolve(), reject);
  });
};

// Sequence Management
export const createSequence = async (sequenceData) => {
  console.log('Creating sequence with URL:', API_URLS.createSequence);
  console.log('Sequence data:', sequenceData);
  try {
    const result = await Post(API_URLS.createSequence, sequenceData);
    console.log('Create sequence result:', result);
    return result;
  } catch (error) {
    console.error('Error in createSequence:', error);
    throw error;
  }
};



export const getAllSequences = async () => {
  try {
    const result = await Get(API_URLS.getAllSequences);
    return result || [];
  } catch (error) {
    console.error('Error fetching sequences:', error);
    return [];
  }
};

export const getSequenceById = async (id) => {
  return await Get(API_URLS.getSequenceById.replace(':id', id));
};

export const updateSequence = async (id, sequenceData) => {
  return await Put(API_URLS.updateSequence.replace(':id', id), sequenceData);
};

export const deleteSequence = async (id) => {
  return await Delete(API_URLS.deleteSequence.replace(':id', id));
};

export const startSequence = async (sequenceId, candidateIds) => {
  return await Post(API_URLS.startSequence.replace(':id', sequenceId), {
    candidateIds
  });
};

export const startSequenceWithTestCandidates = async (sequenceId) => {
  return await Post(`/sequence/${sequenceId}/start-with-test-candidates`);
};

export const startSequenceWithRoleCandidates = async (sequenceId, roleId, limit = 10) => {
  return await Post(`/sequence/${sequenceId}/start-with-role-candidates`, {
    roleId,
    limit
  });
};

export const getRoleCandidates = async (roleId, userId) => {
  return await Get({
    roleId,
    userId
  }, '/role-candidates/get-role-candidates');
};

export const getSequenceWithSteps = async (sequenceId) => {
  return await Get(API_URLS.getSequenceWithSteps.replace(':id', sequenceId));
};

export const getSequenceStats = async (sequenceId) => {
  return await Get(API_URLS.getSequenceStats.replace(':id', sequenceId));
};

// Sequence Steps Management
export const createSequenceStep = async (stepData) => {
  console.log('Creating sequence step with URL:', API_URLS.createSequenceStep);
  console.log('Step data:', stepData);
  try {
    const result = await Post(API_URLS.createSequenceStep, stepData);
    console.log('Create sequence step result:', result);
    return result;
  } catch (error) {
    console.error('Error in createSequenceStep:', error);
    throw error;
  }
};

export const getAllSequenceSteps = async () => {
  return await Get(API_URLS.getAllSequenceSteps);
};

export const getSequenceStepById = async (id) => {
  return await Get(API_URLS.getSequenceStepById.replace(':id', id));
};

export const updateSequenceStep = async (id, stepData) => {
  return await Put(API_URLS.updateSequenceStep.replace(':id', id), stepData);
};

export const deleteSequenceStep = async (id) => {
  return await Delete(API_URLS.deleteSequenceStep.replace(':id', id));
};

// Queue Management
export const getQueueStats = async () => {
  try {
    const result = await Get(API_URLS.getQueueStats);
    return result || {};
  } catch (error) {
    console.error('Error fetching queue stats:', error);
    return {};
  }
};

export const getQueueStatsByName = async (queueName) => {
  return await Get(API_URLS.getQueueStatsByName.replace(':queueName', queueName));
};

export const pauseQueue = async (queueName) => {
  return await Post(API_URLS.pauseQueue.replace(':queueName', queueName));
};

export const resumeQueue = async (queueName) => {
  return await Post(API_URLS.resumeQueue.replace(':queueName', queueName));
};

export const addTestJob = async (queueName, jobData) => {
  return await Post(API_URLS.addTestJob.replace(':queueName', queueName), jobData);
};

// Webhooks
export const sendTestWebhook = async (candidateId, stepId, eventData) => {
  return await Post(
    API_URLS.testWebhook
      .replace(':candidateId', candidateId)
      .replace(':stepId', stepId),
    eventData
  );
};

// Candidate Sequence Status
export const getCandidateSequenceStatus = async (candidateId, sequenceId) => {
  return await Get(
    API_URLS.getCandidateSequenceStatus
      .replace(':candidateId', candidateId)
      .replace(':sequenceId', sequenceId)
  );
};

export const getStatusByType = async (status, limit = 100) => {
  return await Get(`${API_URLS.getStatusByType.replace(':status', status)}?limit=${limit}`);
};

// Follow-up Management
export const createFollowUpSteps = async (stepId, followUpStepsData) => {
  return await Post(API_URLS.createFollowUpSteps.replace(':stepId', stepId), followUpStepsData);
};

export const getFollowUpSteps = async (stepId) => {
  return await Get(API_URLS.getFollowUpSteps.replace(':stepId', stepId));
};

export const triggerManualFollowUp = async (candidateId, stepId) => {
  return await Post(
    API_URLS.triggerManualFollowUp
      .replace(':candidateId', candidateId)
      .replace(':stepId', stepId)
  );
};

export const hasRespondedToSequence = async (candidateId, sequenceId) => {
  return await Get(
    API_URLS.hasRespondedToSequence
      .replace(':candidateId', candidateId)
      .replace(':sequenceId', sequenceId)
  );
};

// Utility functions for sequence execution
export const saveSequenceFromFlow = async (roleId, nodes, edges, userId) => {
  try {
    // Convert ReactFlow nodes and edges to sequence steps
    const sequenceSteps = convertFlowToSequenceSteps(nodes, edges);

    // Validate userId
    if (!userId) {
      throw new Error('User ID is required for sequence creation');
    }

    // Create sequence data
    const sequenceData = {
      name: `Sequence for Role ${roleId}`,
      description: 'Auto-generated from visual flow',
      status: 'ACTIVE',
      userId: userId, // Use real user ID
      roleId: roleId
    };

    // Create sequence first
    const sequence = await createSequence(sequenceData);

    // Create sequence steps
    const createdSteps = [];
    for (const step of sequenceSteps) {
      const stepWithSequenceId = {
        ...step,
        roleSequenceId: sequence.id
      };
      const createdStep = await createSequenceStep(stepWithSequenceId);
      createdSteps.push(createdStep);
    }

    return { sequence, steps: createdSteps };
  } catch (error) {
    console.error('Error saving sequence:', error);
    throw error;
  }
};

// Helper function to convert ReactFlow to sequence steps
const convertFlowToSequenceSteps = (nodes, _edges) => {
  const steps = [];

  // Filter out non-communication nodes (like Start, End, etc.)
  const validCommunicationTypes = [
    'EMAIL', 'EMAIL_STEP',
    'SMS', 'SMS_STEP',
    'WHATSAPP', 'WHATSAPP_STEP',
    'CALL', 'CALL_STEP',
    'LI_CONNECTION', 'LI_INMAIL',
    'LINKEDIN', 'LINKEDIN_STEP'
  ];
  const communicationNodes = nodes.filter(node => {
    const nodeType = node.data.label.toUpperCase().replace(/\s+/g, '_');
    return validCommunicationTypes.includes(nodeType);
  });

  // Group communication nodes by their order (y-position based)
  const nodesByOrder = {};
  communicationNodes.forEach(node => {
    const order = Math.floor(node.position.y / 150); // Group by 150px intervals
    if (!nodesByOrder[order]) {
      nodesByOrder[order] = [];
    }
    nodesByOrder[order].push(node);
  });

  // Convert to sequence steps with deduplication
  const seenSteps = new Set(); // Track unique steps to prevent duplicates

  Object.keys(nodesByOrder).forEach((order, orderIndex) => {
    const nodesAtOrder = nodesByOrder[order];

    nodesAtOrder.forEach(node => {
      const nodeLabel = node.data.label.toUpperCase().replace(/\s+/g, '_');

      // Create unique identifier for this step
      const stepKey = `${nodeLabel}_${orderIndex}`;

      if (seenSteps.has(stepKey)) {
        console.warn(`Duplicate step detected: ${stepKey}, skipping`);
        return;
      }

      // Map frontend labels to backend enum values
      const mediumMapping = {
        'EMAIL': 'EMAIL',
        'EMAIL_STEP': 'EMAIL',
        'SMS': 'SMS',
        'SMS_STEP': 'SMS',
        'WHATSAPP': 'WHATSAPP',
        'WHATSAPP_STEP': 'WHATSAPP',
        'CALL': 'CALL',
        'CALL_STEP': 'CALL',
        'LI_CONNECTION': 'LINKEDIN',
        'LI_INMAIL': 'LINKEDIN',
        'LINKEDIN': 'LINKEDIN',
        'LINKEDIN_STEP': 'LINKEDIN'
      };

      const mappedMedium = mediumMapping[nodeLabel];

      if (!mappedMedium) {
        console.warn(`Unknown medium type: ${nodeLabel}, skipping step`);
        return;
      }

      seenSteps.add(stepKey);
      const step = {
        name: node.data.label,
        order: orderIndex, // Use sequential order index
        type: 'OUTREACH',
        medium: mappedMedium,
        templateId: 1,
        roleSequenceId: null // Will be set when saving
      };
      steps.push(step);
    });
  });

  return steps;
};

export default {
  createSequence,
  getAllSequences,
  getSequenceById,
  updateSequence,
  deleteSequence,
  startSequence,
  getSequenceWithSteps,
  getSequenceStats,
  createSequenceStep,
  getAllSequenceSteps,
  getSequenceStepById,
  updateSequenceStep,
  deleteSequenceStep,
  getQueueStats,
  getQueueStatsByName,
  pauseQueue,
  resumeQueue,
  addTestJob,
  sendTestWebhook,
  getCandidateSequenceStatus,
  getStatusByType,
  saveSequenceFromFlow
};
