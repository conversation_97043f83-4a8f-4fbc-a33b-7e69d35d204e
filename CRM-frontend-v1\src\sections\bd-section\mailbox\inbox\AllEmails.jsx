import React, { useEffect, useState } from 'react';
import EmailPage from './EmailPage';
import { Get } from 'actions/API/apiActions';
import { API_URLS } from 'constants/apiUrls';
import { useOutletContext } from 'react-router';
import { message } from 'antd';

function AllEmails(props) {
  const { selectedMailboxEmail } = useOutletContext();
  const [isLoading, setIsLoading] = useState(false);
  const [emails, setEmails] = useState([]);
  const handleGetAllEmails = () => {
    try {
      setIsLoading(true);
      Get(
        {
          email: selectedMailboxEmail ? selectedMailboxEmail : '',
          type: 'INBOX'
        },
        API_URLS.getAllEmailsByEmailType,
        (resp) => {
          setEmails(resp);

          setIsLoading(false);
        },
        (error) => {
          console.log('erdsdsadror', error);
          message.error(error?.response?.message || 'Failed to get emails. Try again!');
          setIsLoading(false);
        }
      );
    } catch (error) {
      message.error('Failed to get emails. Try again!');
      setIsLoading(false);
    }
  };

  useEffect(() => {
    handleGetAllEmails();
  }, [selectedMailboxEmail]);
  return (
    <div>
      <EmailPage emails={emails} onEmailsUpdate={(emails) => setEmails(emails)} isLoading={isLoading} type="Inbox" />
    </div>
  );
}

export default AllEmails;
