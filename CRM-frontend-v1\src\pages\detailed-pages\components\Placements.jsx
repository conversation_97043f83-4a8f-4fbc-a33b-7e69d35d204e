import { Icon } from '@iconify/react';
import { Input, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import PlacementsTable from './PlacementsTable';
import { Get } from 'actions/API/apiActions';
import { API_URLS } from 'constants/apiUrls';
import { Helmet } from 'react-helmet-async';

function Placements(props) {
  const [searchString, setSearchString] = useState('');
  const getRolePlacements = () => {
    try {
      Get(
        {},
        API_URLS.login,
        (response) => {
          // console.log('response', response);
        },
        (error) => {
          // console.log('error', error);
          message.error(error?.response?.message || 'Failed to fetch data');
        }
      );
    } catch (error) {
      message.error('An error occurred while fetching placements');
    }
  };

  useEffect(() => {
    getRolePlacements();
  }, []);
  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <Helmet>
        <title>Role Placements</title>
      </Helmet>
      <Row>
        <Input
          variant="borderless"
          placeholder="Search name"
          style={{ backgroundColor: 'white', width: '300px', borderRadius: 0, height: '40px' }}
          suffix={<Icon icon="ant-design:search-outlined" width="18" height="18" style={{ cursor: 'pointer' }} />}
          onChange={(e) => setSearchString(e.target.value)}
          value={searchString}
        />
      </Row>
      <PlacementsTable />
    </div>
  );
}

export default Placements;
