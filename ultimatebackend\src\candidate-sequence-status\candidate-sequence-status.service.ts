import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { CandidateSequenceStatus, SequenceStepStatus, StepType } from './candidate-sequence-status.entity';
import { RoleSequence } from 'src/sequence/sequence.entity';
import { SequenceSteps } from 'src/sequence-steps/sequence_steps.entity';
import { RoleCandidate } from 'src/role_candidates/role_candidates.entity';
import { People } from 'src/people/people.entity';

@Injectable()
export class CandidateSequenceStatusService {
  constructor(
    @InjectRepository(CandidateSequenceStatus)
    private readonly candidateSequenceStatusRepository: Repository<CandidateSequenceStatus>,
    @InjectRepository(RoleSequence)
    private readonly sequenceRepository: Repository<RoleSequence>,
    @InjectRepository(SequenceSteps)
    private readonly stepRepository: Repository<SequenceSteps>,
    @InjectRepository(RoleCandidate)
    private readonly candidateRepository: Repository<RoleCandidate>,
    @InjectRepository(People)
    private readonly peopleRepository: Repository<People>,
  ) {}

  async createCandidateSequenceStatus(
    candidateId: number,
    sequenceId: number,
    stepId: number,
    stepType: StepType = StepType.SEQUENTIAL,
    stepOrder: number = 0,
  ): Promise<CandidateSequenceStatus> {
    // Validate that all foreign key references exist
    await this.validateForeignKeyReferences(candidateId, sequenceId, stepId);

    const status = this.candidateSequenceStatusRepository.create({
      candidateId,
      sequenceId,
      stepId,
      stepType,
      stepOrder,
      status: SequenceStepStatus.PENDING,
    });

    try {
      return await this.candidateSequenceStatusRepository.save(status);
    } catch (error) {
      console.error(`Failed to create candidate sequence status:`, {
        candidateId,
        sequenceId,
        stepId,
        error: error.message
      });
      throw new Error(`Failed to create candidate sequence status: ${error.message}`);
    }
  }

  /**
   * Validate that all foreign key references exist before creating candidate sequence status
   */
  private async validateForeignKeyReferences(
    candidateId: number,
    sequenceId: number,
    stepId: number,
  ): Promise<void> {
    // Check if candidate exists
    const candidate = await this.candidateRepository.findOne({
      where: { id: candidateId },
    });
    if (!candidate) {
      throw new Error(`Candidate with ID ${candidateId} not found`);
    }

    // Check if sequence exists
    const sequence = await this.sequenceRepository.findOne({
      where: { id: sequenceId },
    });
    if (!sequence) {
      throw new Error(`Sequence with ID ${sequenceId} not found`);
    }

    // Check if step exists
    const step = await this.stepRepository.findOne({
      where: { id: stepId },
    });
    if (!step) {
      throw new Error(`Step with ID ${stepId} not found`);
    }

    // Check if step belongs to the sequence
    if (step.roleSequenceId !== sequenceId) {
      throw new Error(`Step ${stepId} does not belong to sequence ${sequenceId}. Step belongs to sequence ${step.roleSequenceId}`);
    }
  }

  async findById(id: number): Promise<CandidateSequenceStatus | null> {
    return await this.candidateSequenceStatusRepository.findOne({
      where: { id },
      relations: ['candidate', 'sequence', 'step'],
    });
  }

  async updateStatus(
    id: number,
    status: SequenceStepStatus,
    metadata?: Record<string, any>,
  ): Promise<CandidateSequenceStatus> {
    const candidateStatus = await this.candidateSequenceStatusRepository.findOne({
      where: { id },
    });

    if (!candidateStatus) {
      // Log additional debugging information
      console.error(`❌ CANDIDATE-SEQUENCE-STATUS: Record with ID ${id} not found in database`);

      // Try to find any records that might be related
      const allRecords = await this.candidateSequenceStatusRepository.find({
        take: 10,
        order: { id: 'DESC' }
      });

      console.error(`❌ CANDIDATE-SEQUENCE-STATUS: Recent records in database:`,
        allRecords.map(r => ({ id: r.id, candidateId: r.candidateId, stepId: r.stepId, status: r.status }))
      );

      throw new NotFoundException(`Candidate sequence status not found for ID ${id}`);
    }

    candidateStatus.status = status;
    if (metadata) {
      candidateStatus.metadata = { ...candidateStatus.metadata, ...metadata };
    }

    // Update timestamp based on status
    const now = new Date();
    switch (status) {
      case SequenceStepStatus.QUEUED:
        candidateStatus.scheduledAt = now;
        break;
      case SequenceStepStatus.SENT:
        candidateStatus.sentAt = now;
        break;
      case SequenceStepStatus.DELIVERED:
        candidateStatus.deliveredAt = now;
        break;
      case SequenceStepStatus.OPENED:
        candidateStatus.openedAt = now;
        break;
      case SequenceStepStatus.CLICKED:
        candidateStatus.clickedAt = now;
        break;
      case SequenceStepStatus.REPLIED:
        candidateStatus.repliedAt = now;
        break;
      case SequenceStepStatus.COMPLETED:
        candidateStatus.completedAt = now;
        break;
      case SequenceStepStatus.FAILED:
        candidateStatus.failedAt = now;
        break;
    }

    return await this.candidateSequenceStatusRepository.save(candidateStatus);
  }

  async getCandidateSequenceStatus(
    candidateId: number,
    sequenceId: number,
  ): Promise<CandidateSequenceStatus[]> {
    return await this.candidateSequenceStatusRepository.find({
      where: { candidateId, sequenceId },
      relations: ['candidate', 'sequence', 'step'],
      order: { stepOrder: 'ASC' },
    });
  }

  async getNextSteps(
    candidateId: number,
    sequenceId: number,
    currentOrder: number,
  ): Promise<CandidateSequenceStatus[]> {
    return await this.candidateSequenceStatusRepository.find({
      where: {
        candidateId,
        sequenceId,
        stepOrder: currentOrder + 1,
        status: SequenceStepStatus.PENDING,
      },
      relations: ['step'],
    });
  }

  async getPendingSteps(
    candidateId: number,
    sequenceId: number,
    stepOrder: number,
  ): Promise<CandidateSequenceStatus[]> {
    return await this.candidateSequenceStatusRepository.find({
      where: {
        candidateId,
        sequenceId,
        stepOrder,
        status: SequenceStepStatus.PENDING,
      },
      relations: ['step'],
    });
  }

  async markStepCompleted(
    candidateId: number,
    stepId: number,
    responseData?: string,
  ): Promise<CandidateSequenceStatus> {
    const status = await this.candidateSequenceStatusRepository.findOne({
      where: { candidateId, stepId },
    });

    if (!status) {
      throw new NotFoundException('Candidate sequence status not found');
    }

    status.status = SequenceStepStatus.COMPLETED;
    status.completedAt = new Date();
    if (responseData) {
      status.responseData = responseData;
    }

    return await this.candidateSequenceStatusRepository.save(status);
  }

  async incrementAttemptCount(id: number): Promise<void> {
    await this.candidateSequenceStatusRepository.increment(
      { id },
      'attemptCount',
      1,
    );
  }

  async getStepsByStatus(
    status: SequenceStepStatus,
    limit: number = 100,
  ): Promise<CandidateSequenceStatus[]> {
    return await this.candidateSequenceStatusRepository.find({
      where: { status },
      relations: ['candidate', 'sequence', 'step'],
      take: limit,
      order: { createdAt: 'ASC' },
    });
  }

  async getCandidatesInSequence(sequenceId: number): Promise<CandidateSequenceStatus[]> {
    return await this.candidateSequenceStatusRepository.find({
      where: { sequenceId },
      select: ['candidateId', 'sequenceId'], // Only select what we need for performance
      order: { candidateId: 'ASC' },
    });
  }

  /**
   * Get candidates in sequence with their names and details for dashboard display
   */
  async getCandidatesInSequenceWithNames(sequenceId: number): Promise<any[]> {
    try {
      // First get the candidate sequence statuses
      const candidateStatuses = await this.candidateSequenceStatusRepository.find({
        where: { sequenceId },
        order: { candidateId: 'ASC', stepOrder: 'ASC' }
      });

      console.log(`Found ${candidateStatuses.length} candidate statuses for sequence ${sequenceId}`);

      // Get unique candidate IDs
      const candidateIds = [...new Set(candidateStatuses.map(cs => cs.candidateId))];
      console.log('Candidate IDs:', candidateIds);

      // Get role candidates
      const roleCandidates = await this.candidateRepository.find({
        where: candidateIds.map(id => ({ id })),
        relations: ['candidate'] // This should load the People entity
      });

      console.log(`Found ${roleCandidates.length} role candidates`);

      // Get sequence steps
      const stepIds = [...new Set(candidateStatuses.map(cs => cs.stepId).filter(id => id))];
      const steps = await this.stepRepository.find({
        where: stepIds.map(id => ({ id }))
      });

      console.log(`Found ${steps.length} steps`);

      // Create lookup maps
      const candidateMap = new Map();
      roleCandidates.forEach(rc => {
        candidateMap.set(rc.id, {
          id: rc.id,
          candidateId: rc.candidateId,
          candidate: rc.candidate
        });
      });

      const stepMap = new Map();
      steps.forEach(step => {
        stepMap.set(step.id, step);
      });

      // Transform the results
      return candidateStatuses.map(status => {
        const roleCandidate = candidateMap.get(status.candidateId);
        const step = stepMap.get(status.stepId);
        const person = roleCandidate?.candidate;

        return {
          id: status.id,
          candidateId: status.candidateId,
          candidateName: this.formatCandidateName(
            person?.first_name,
            person?.last_name,
            person?.full_name
          ),
          candidateTitle: person?.current_title || '',
          candidateProfileUrl: person?.profile_url || '',
          sequenceId: status.sequenceId,
          stepId: status.stepId,
          stepName: step?.name || 'Unknown Step',
          stepMedium: step?.medium || 'UNKNOWN',
          stepOrder: status.stepOrder || 0,
          status: status.status,
          attemptCount: status.attemptCount || 0,
          createdAt: status.createdAt,
          updatedAt: status.updatedAt,
          lastActivity: status.updatedAt,
          currentStep: {
            id: status.stepId,
            name: step?.name || 'Unknown Step',
            medium: step?.medium || 'UNKNOWN',
            order: status.stepOrder || 0
          }
        };
      });
    } catch (error) {
      console.error('Error in getCandidatesInSequenceWithNames:', error);
      // Return fallback data
      return [];
    }
  }

  /**
   * Format candidate name from available name fields
   */
  private formatCandidateName(firstName: string, lastName: string, fullName: string): string {
    if (fullName) {
      return fullName;
    }

    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    }

    if (firstName) {
      return firstName;
    }

    if (lastName) {
      return lastName;
    }

    return 'Unknown Candidate';
  }

  async initializeCandidateSequence(
    candidateIds: number[],
    sequenceId: number,
  ): Promise<CandidateSequenceStatus[]> {
    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Initializing candidate sequence for ${candidateIds.length} candidates and sequence ${sequenceId}`);

    // Validate input parameters
    if (!candidateIds || candidateIds.length === 0) {
      console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ No candidate IDs provided`);
      throw new Error('No candidate IDs provided');
    }

    if (!sequenceId) {
      console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ No sequence ID provided`);
      throw new Error('No sequence ID provided');
    }

    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Fetching sequence ${sequenceId} with steps...`);

    // Get sequence with steps
    const sequence = await this.sequenceRepository.findOne({
      where: { id: sequenceId },
      relations: ['sequenceSteps'],
    });

    if (!sequence) {
      console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Sequence with ID ${sequenceId} not found`);
      throw new NotFoundException(`Sequence with ID ${sequenceId} not found`);
    }

    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Found sequence "${sequence.name}" (ID: ${sequence.id})`);

    if (!sequence.sequenceSteps || sequence.sequenceSteps.length === 0) {
      console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Sequence ${sequenceId} has no steps defined`);
      throw new Error(`Sequence ${sequenceId} has no steps defined`);
    }

    // Sort steps by order
    const sortedSteps = sequence.sequenceSteps.sort((a, b) => a.order - b.order);
    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Found ${sortedSteps.length} steps for sequence ${sequenceId}`);
    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Steps details:`, sortedSteps.map(s => ({
      id: s.id,
      name: s.name,
      order: s.order,
      medium: s.medium,
      type: s.type,
      templateId: s.templateId
    })));

    // Validate that all candidates exist before proceeding
    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Validating ${candidateIds.length} candidates exist...`);
    for (const candidateId of candidateIds) {
      const candidate = await this.candidateRepository.findOne({
        where: { id: candidateId },
      });
      if (!candidate) {
        console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Candidate with ID ${candidateId} not found`);
        throw new Error(`Candidate with ID ${candidateId} not found`);
      }
      console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Validated candidate ${candidateId}`);
    }

    const statusEntries: CandidateSequenceStatus[] = [];

    for (const candidateId of candidateIds) {
      console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Processing candidate ${candidateId}`);

      for (const step of sortedSteps) {
        try {
          // Determine step type based on order grouping
          const stepsAtSameOrder = sortedSteps.filter(s => s.order === step.order);
          const stepType = stepsAtSameOrder.length > 1 ? StepType.PARALLEL : StepType.SEQUENTIAL;

          console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: Creating status for candidate ${candidateId}, step ${step.id} (${step.name}), order ${step.order}, medium ${step.medium}, type ${stepType}`);

          const status = await this.createCandidateSequenceStatus(
            candidateId,
            sequenceId,
            step.id,
            stepType,
            step.order,
          );

          console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Created status record ID ${status.id} for candidate ${candidateId}, step ${step.id}`);
          statusEntries.push(status);
        } catch (error) {
          console.error(`🔄 CANDIDATE-SEQUENCE-STATUS: ❌ Failed to create status for candidate ${candidateId}, step ${step.id}:`, error.message);
          throw error;
        }
      }
    }

    console.log(`🔄 CANDIDATE-SEQUENCE-STATUS: ✅ Successfully created ${statusEntries.length} candidate sequence status entries`);
    return statusEntries;
  }
}
