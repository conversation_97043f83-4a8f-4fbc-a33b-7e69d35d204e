import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Company } from 'src/company/company.entity';
import { ILike, IsNull, Not, Repository } from 'typeorm';
import {
  CreateCompanyByScrapperDto,
  UpdateCompanyByScrapperDto,
} from './dto/createCompanyByScrapper.dto';
import { Country } from 'src/country/country.entity';
import { AddPersonByScrapperDto } from './dto/addPersonByScrapper.dto';
import { People } from 'src/people/people.entity';
import { PersonEmail } from 'src/emails/emails.entity';
import { PeopleAssignment } from 'src/people-assignments/entities/people-assignment.entity';
import { PersonPhone } from 'src/phone/phone.entity';
import { Jobs } from 'src/jobs/jobs.entity';
import { AddJobByScrapperDto } from './dto/addJobByScrapper.dto';
import { usStates } from 'src/helper/UsStates';
import {
  PersonSource,
  PersonStatusType,
  PersonType,
} from 'src/people/dto/people.enums';
import { ScrapperStatsDto } from './dto/scrapperStats.dto';
import { ScrapperStats } from './scrapperStats.entity';
import { GetDailyScrapperReportDto } from './dto/getDailyScrapper.dto';
import { DataSource } from 'typeorm';
import { City, State, Country as CSCCountry } from 'country-state-city';
import { GetComanyControlDto } from './dto/getComanyControl.dto';
import { CompanyScrapperControl } from 'src/company_scrapper_control/entities/company_scrapper_control.entity';
import { PersonPhoneType } from 'src/phone/dto/person-phone.dto';

@Injectable()
export class ScrapperService {
  constructor(
    @InjectRepository(Company) private companyRepository: Repository<Company>,
    @InjectRepository(Country) private countryRepository: Repository<Country>,
    @InjectRepository(People) private peopleRepository: Repository<People>,
    @InjectRepository(PersonEmail)
    private personEmailRepository: Repository<PersonEmail>,
    @InjectRepository(PeopleAssignment)
    private peopleAssignmentRepository: Repository<PeopleAssignment>,
    @InjectRepository(PersonPhone)
    private personPhoneRepository: Repository<PersonPhone>,
    @InjectRepository(Jobs) private jobsRepository: Repository<Jobs>,
    @InjectRepository(ScrapperStats)
    private scrapperStatsRepository: Repository<ScrapperStats>,
    @InjectRepository(CompanyScrapperControl)
    private companyScrapperControlRepository: Repository<CompanyScrapperControl>,
    @InjectRepository(PersonPhone)
    private phoneRepository: Repository<PersonPhone>,
    private readonly dataSource: DataSource,
  ) {}

  async createCompany(data: CreateCompanyByScrapperDto) {
    try {
      const processedProfileUrl = data.profile_url.replace('/life', '');

      const existingCompany = await this.companyRepository.findOne({
        where: {
          profile_url: ILike(`%${processedProfileUrl}%`),
          name: data.name,
        },
      });

      if (existingCompany) {
        console.log('Company already exists');
        existingCompany.region = data.region;
        await this.companyRepository.save(existingCompany);
        return {
          message: 'Company already exists',
          company: existingCompany,
        };
      }

      let c_id = null;

      // Get list of countries
      const countries = await this.countryRepository.find({
        select: ['id', 'name'],
      });

      // Try to find country by matching country name in address
      const country = countries.find((country) =>
        data.address.toLowerCase().includes(country.name.toLowerCase()),
      );

      if (country) {
        c_id = country.id;
      } else {
        // Fallback: Try to find existing company with similar address and get its country
        const company = await this.companyRepository.findOne({
          where: {
            address: ILike(`%${data.address}%`),
          },
          relations: ['country'], // make sure to load the country relation!
        });
        if (company && company.country) {
          c_id = company.country.id;
        }
      }

      const newCompany = this.companyRepository.create({
        ...data,
        countryId: c_id,
        company_phone: data.phone_number,
        founded: data.founded_on,
        cover_photo: data.cover,
        profile_url: processedProfileUrl,
        sectorId: data.sector_id,
        scrapper_profile_name: data.scrapper_profile_name,
      });

      const savedCompany = await this.companyRepository.save(newCompany);
      console.log('New Company created by Scrapper !!!');
      return {
        message: 'Company created successfully',
        company: savedCompany,
      };
    } catch (error) {
      console.log('ERROR to create company: ', error);
      throw new InternalServerErrorException(
        `Failed to create new company: ${error.message}`,
      );
    }
  }

  async getCountryIdFromCity(cityName: string): Promise<number> {
    const parts = cityName.split(',').map((part) => part.trim());

    if (parts.includes('New York')) {
      return 2;
    }

    if (parts.includes('London')) {
      return 1;
    }

    const allCities = City.getAllCities();
    let matchedCity = null;
    if (parts.length >= 2) {
      const cityPart = parts[0];
      const regionOrCountryPart = parts[1];

      const allCountries = CSCCountry.getAllCountries();
      const possibleCountry = allCountries.find(
        (country) =>
          country.name.toLowerCase() === regionOrCountryPart.toLowerCase() ||
          country.name
            .toLowerCase()
            .includes(regionOrCountryPart.toLowerCase()),
      );

      if (possibleCountry) {
        matchedCity = allCities.find(
          (city) =>
            city.name.toLowerCase() === cityPart.toLowerCase() &&
            city.countryCode === possibleCountry.isoCode,
        );
      }

      if (!matchedCity) {
        const allStates = State.getAllStates();
        const possibleState = allStates.find(
          (state) =>
            state.name.toLowerCase() === regionOrCountryPart.toLowerCase(),
        );

        if (possibleState) {
          matchedCity = allCities.find(
            (city) =>
              city.name.toLowerCase() === cityPart.toLowerCase() &&
              city.stateCode === possibleState.isoCode &&
              city.countryCode === possibleState.countryCode,
          );
        }
      }
    }

    if (!matchedCity) {
      const cityPart = parts[0];
      const citiesWithName = allCities.filter(
        (city) => city.name.toLowerCase() === cityPart.toLowerCase(),
      );

      if (citiesWithName.length > 1) {
        citiesWithName.forEach((city) => {
          const country = CSCCountry.getCountryByCode(city.countryCode);
        });

        matchedCity =
          citiesWithName.find((city) => city.countryCode === 'GB') ||
          citiesWithName[0];
      } else {
        matchedCity = citiesWithName[0];
      }
    }

    if (!matchedCity) {
      console.log('City not found: ', cityName);
      return null;
    }

    // Get country by country code
    const country = CSCCountry.getCountryByCode(matchedCity.countryCode);

    if (!country) {
      console.log('Country not found: ', matchedCity.countryCode);
      return null;
    }

    // Find in your database
    const dbCountry = await this.countryRepository.findOne({
      where: { name: country.name },
    });

    if (!dbCountry) {
      return null;
    }

    return dbCountry.id;
  }

  async updateCompany(data: UpdateCompanyByScrapperDto) {
    try {
      const processedProfileUrl = data.profile_url.replace('/life', '');

      const existingCompany = await this.companyRepository.findOne({
        where: { id: data.id },
      });

      if (!existingCompany) {
        console.log('Company not found');
        throw new Error('Company not found');
      }

      let countryId = null;

      if (existingCompany.countryId === null) {
        countryId = await this.getCountryIdFromCity(data.headquarter_country);
        if (countryId === null) {
          countryId = null;
        }
      }

      const updateCompany = await this.companyRepository.update(
        { id: data.id },
        {
          public_id: data.public_id,
          profile_url: data.profile_url
            ? data.profile_url.replace('/life', '')
            : undefined, // processed profile url
          profile_url_encoded: data.profile_url_encoded,
          logo: data.logo,
          cover_photo: data.cover,
          company_phone: data.phone_number,
          website: data.website,
          tagline: data.tagline,
          staff_count: data.staff_count,
          staff_count_range_start: data.staff_count_range_start,
          staff_count_range_end: data.staff_count_range_end,
          followers_count: isNaN(data.follower_count)
            ? null
            : data.follower_count,
          description: data.description,
          founded: data.founded_on,
          headquarter_country: data.headquarter_country,
          headquarter_city: data.headquarter_city,
          headquarter_geographic_area: data.headquarter_geographic_area,
          headquarter_line1: data.headquarter_line1,
          headquarter_line2: data.headquarter_line2,
          headquarter_postal_code: data.headquarter_postal_code,
          industry: data.industry,
          specialities: data.specialities,
          company_email: data.company_email,
          is_scrapped_fully: data.is_scrapped_fully,
          scrapper_level: data.scrapper_level,
          sectorId: data.sector_id,
          scrapper_profile_name: data.scrapper_profile_name,
          countryId:
            existingCompany.countryId === null
              ? countryId
              : existingCompany.countryId,
        },
      );

      console.log('Company updated by Scrapper !!!');

      const updatedCompany = await this.companyRepository.findOne({
        where: { id: data.id },
      });

      return {
        message: 'Company updated successfully',
        company: updatedCompany,
      };
    } catch (error) {
      console.log('ERROR to update company: ', error);
      await this.companyRepository.update(
        { id: data.id },
        {
          is_scrapped_fully: false,
          scrapper_level: 2,
        },
      );
      throw new InternalServerErrorException(
        `Failed to update company: ${error.message}`,
      );
    }
  }

  async addPersonByScrapper(data: AddPersonByScrapperDto) {
    try {
      if (!data.full_name || !data.first_name || !data.last_name) {
        throw new Error('Full name, first name, and last name are required.');
      }

      if (!data.company_id) {
        throw new Error('Company ID is required.');
      }

      if (!data.sector_id) {
        throw new Error('Sector ID is required.');
      }

      const peopleCount = await this.peopleRepository.count({
        where: {
          companyId: data.company_id,
        },
      });

      if (peopleCount >= 200) {
        throw new Error(
          'You have reached the maximum limit of people against this company.',
        );
      }

      const existingPerson = await this.peopleRepository.findOne({
        where: {
          profile_url: data.profile_url,
        },
      });

      if (existingPerson) {
        const personId = existingPerson.id;
        await this.peopleRepository.update(personId, {
          profile_img: data.avator,
        });

        if (data.email) {
          const existingEmail = await this.personEmailRepository.findOne({
            where: {
              email: data.email,
              personId: personId,
            },
          });
          if (!existingEmail) {
            await this.personEmailRepository.save({
              email: data.email,
              email_type: 'BUSINESS',
              personId: personId,
            });
          }
        }

        if (data.phone_number) {
          const existingPhone = await this.personPhoneRepository.findOne({
            where: {
              phone_number: data.phone_number,
              personId: personId,
            },
          });
          if (!existingPhone) {
            await this.personPhoneRepository.save({
              phone_number: data.phone_number,
              personId: personId,
              phone_type: 'BUSINESS',
            });
          }
        }

        console.log('Person with LinkedIn profile already exists.');
        return {
          message: 'Person with LinkedIn profile already exists.',
          person: existingPerson,
          found: true,
        };
      }

      let c_id = null;
      const countries = await this.countryRepository.find({
        select: ['id', 'name'],
      });

      const country = countries.find((country) => {
        data.address.toLowerCase().includes(country.name.toLowerCase());
      });
      if (country) {
        c_id = country.id;
      }

      const newPerson = this.peopleRepository.create({
        full_name: data.full_name,
        first_name: data.first_name,
        last_name: data.last_name,
        headline: data.headline,
        profile_img: data.avator,
        profile_url: data.profile_url,
        current_title: data.current_title,
        SR_specied_industry: data.SR_specied_industry,
        industry: data.industry,
        summary: data.summary,
        location: data.address,
        is_hiring: data.is_hiring_person == 'true' ? true : false,
        companyId: data.company_id,
        sectorId: Number(data.sector_id),
        countryId: c_id ? c_id : data.country_id,
        profile_source: PersonSource.JOB_POST_SCRAPPER,
        person_type: PersonType.JOB_POST_LEAD,
        scrapper_profile_name: data.scrapper_profile_name,
      });

      if (newPerson) {
        if (data.email) {
          await this.personEmailRepository.save({
            email: data.email,
            email_type: 'BUSINESS',
            personId: newPerson.id,
          });
        }

        if (data.phone_number) {
          await this.personPhoneRepository.save({
            phone_number: data.phone_number,
            personId: newPerson.id,
            phone_type: 'BUSINESS',
          });
        }
      }
      console.log('New person created by scrapper!');
      await this.peopleRepository.save(newPerson);
      return {
        message: 'Person added successfully',
        person: newPerson,
        found: true,
      };
    } catch (error) {
      console.log('ERROR to add person: ', error);
      return {
        message: error.message,
        found: false,
      };
    }
  }

  async addJobByScrapper(data: AddJobByScrapperDto) {
    try {
      // 1) Check required foreign keys
      if (!data.company_id) {
        throw new Error('Company ID is required.');
      }
      if (!data.sector_id) {
        throw new Error('Sector ID is required.');
      }

      // 2) Prevent duplicates by title + company + sector
      const existingJob = await this.jobsRepository.findOne({
        where: {
          title: data.job_title,
          companyId: data.company_id,
          sectorId: data.sector_id,
        },
      });
      console.log('Job post with the same title already exists.');
      if (existingJob) {
        return {
          message: 'Job post with the same title already exists.',
        };
      }

      // 3) Prepare lists of “human‐readable” terms vs. entity enums
      const experienceLevelsMap: Record<
        string,
        | 'INTERN'
        | 'ENTRY_LEVEL'
        | 'ASSOCIATE'
        | 'MID_SENIOR_LEVEL'
        | 'SENIOR_LEVEL'
        | 'DIRECTOR'
        | 'EXECUTIVE'
      > = {
        Internship: 'INTERN',
        'Entry level': 'ENTRY_LEVEL',
        Associate: 'ASSOCIATE',
        'Mid-Senior level': 'MID_SENIOR_LEVEL',
        'Senior level': 'SENIOR_LEVEL',
        Director: 'DIRECTOR',
        Executive: 'EXECUTIVE',
      };
      const jobTypesMap: Record<
        string,
        'FULL_TIME' | 'PART_TIME' | 'CONTRACT' | 'TEMPORARY' | 'INTERNSHIP'
      > = {
        'Full-time': 'FULL_TIME',
        'Part-time': 'PART_TIME',
        Contract: 'CONTRACT',
        Temporary: 'TEMPORARY',
        Internship: 'INTERNSHIP',
      };
      const remoteTypesMap: Record<string, 'ONSITE' | 'REMOTE' | 'HYBRID'> = {
        'On-site': 'ONSITE',
        Remote: 'REMOTE',
        Hybrid: 'HYBRID',
      };

      // 4) Split data.salary_experience_remote into experience_level, job_type, job_location_type (remote), and leftover salary text
      let experience_level:
        | 'INTERN'
        | 'ENTRY_LEVEL'
        | 'ASSOCIATE'
        | 'MID_SENIOR_LEVEL'
        | 'SENIOR_LEVEL'
        | 'DIRECTOR'
        | 'EXECUTIVE' = null;
      let job_type:
        | 'FULL_TIME'
        | 'PART_TIME'
        | 'CONTRACT'
        | 'TEMPORARY'
        | 'INTERNSHIP' = null;
      let job_location_type: 'ONSITE' | 'REMOTE' | 'HYBRID' = null;
      let leftoverSalaryText: string = null;

      if (data.salary_experience_remote) {
        // Build a regex that matches any of the “human‐readable” terms:
        const patternParts = [
          ...Object.keys(experienceLevelsMap),
          ...Object.keys(jobTypesMap),
          ...Object.keys(remoteTypesMap),
        ].map((str) => str.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&'));
        const regex = new RegExp(`(${patternParts.join('|')})`, 'g');

        const matches = data.salary_experience_remote.match(regex);
        leftoverSalaryText = data.salary_experience_remote;
        if (matches) {
          matches.forEach((part) => {
            leftoverSalaryText = leftoverSalaryText.replace(part, '').trim();
            if (experienceLevelsMap[part]) {
              experience_level = experienceLevelsMap[part];
            } else if (jobTypesMap[part]) {
              job_type = jobTypesMap[part];
            } else if (remoteTypesMap[part]) {
              job_location_type = remoteTypesMap[part];
            }
          });
        }
      }

      // 5) Parse the “location” string into city/state
      let city: string = null;
      let state: string = null;
      if (data.location) {
        const ukStates = ['England', 'Scotland', 'Wales', 'Northern Ireland'];
        data.location
          .split(',')
          .map((part) => part.trim())
          .forEach((part) => {
            if (usStates.some((s) => s.full === part || s.short === part)) {
              state = part;
            } else if (ukStates.includes(part)) {
              state = part;
            } else {
              city = part;
            }
          });
      }

      let c_id: number = null;
      if (data.location) {
        const allCountries = await this.countryRepository.find({
          select: ['id', 'name'],
        });
        const found = allCountries.find((country) =>
          data.location.toLowerCase().includes(country.name.toLowerCase()),
        );
        if (found) {
          c_id = found.id;
        } else {
          const fallbackJob = await this.jobsRepository.findOne({
            where: [
              { job_location_country: ILike(`%${data.location}%`) },
              { job_location_state: ILike(`%${data.location}%`) },
              { job_location_city: ILike(`%${data.location}%`) },
            ],
          });
          if (fallbackJob) {
            c_id = fallbackJob.countryId;
          }
        }
      }

      let job_posting_date: Date = null;
      if (data.job_post_date) {
        job_posting_date = new Date(data.job_post_date);
        if (isNaN(job_posting_date.getTime())) {
          throw new Error(`Invalid job_post_date: ${data.job_post_date}`);
        }
      }

      const newJob = this.jobsRepository.create({
        title: data.job_title ?? null,
        description: data.job_description ?? null,
        job_posting_link: data.job_posting_link ?? null,
        job_posting_date: job_posting_date,
        experience_level: experience_level,
        job_type: job_type,
        job_location_type: job_location_type,
        SR_specified_industry: data.SR_specified_industry ?? null,
        applicants: data.applicants ?? null,
        job_location_city: city,
        job_location_state: state,
        job_location_country: data.location ?? null,
        job_location_zip: null,
        currency: null,
        salary_min: null,
        salary_max: null,
        salary_period: null,
        industry: data.industry ?? null,
        skill_required: data.skills_required ?? null,
        skills: data.skills_required
          ? data.skills_required.split(',').map((s) => s.trim())
          : [],
        companyId: data.company_id,
        personId: null,
        sectorId: data.sector_id,
        countryId: c_id,
        scrapper_profile_name: data.scrapper_profile_name,
      });

      const savedJob = await this.jobsRepository.save(newJob);
      console.log('New job post created by scrapper');
      return {
        message: 'Job added successfully.',
        job: savedJob,
      };
    } catch (error) {
      console.error('Error adding job:', error.message);
      throw new InternalServerErrorException(
        `Failed to add job: ${error.message}`,
      );
    }
  }

  async addPersonOnly(data: AddPersonByScrapperDto) {
    console.log('Adding person by scrapper', data.phone_number);

    try {
      if (!data.profile_url) {
        console.log('Profile URL is required.');
        throw new Error('Profile URL is required.');
      }

      if (!data.sector_id) {
        console.log('Sector ID is required.');
        throw new Error('Sector ID is required.');
      }

      // if (data.country_id === 0 || data.country_id === null) {
      //   console.log('Country ID is 0.');
      //   throw new Error(
      //     'Country ID is required. You are sending country id = 0',
      //   );
      // }

      let is_company_id = data.company_id ? Number(data.company_id) : null;
      if (!is_company_id) {
        is_company_id = null;
      }

      const existingPerson = await this.peopleRepository.findOne({
        where: {
          profile_url: ILike(`%${data.profile_url}%`),
        },
      });

      if (existingPerson) {
        console.log('Person already exists');
        return {
          message: 'Person already exists',
          person: existingPerson,
          found: true,
        };
      }

      const newPerson = this.peopleRepository.create({
        full_name: data.full_name,
        first_name: data.first_name,
        last_name: data.last_name,
        headline: data.headline,
        profile_img: data.avator,
        profile_url: data.profile_url,
        current_title: data.current_title,
        SR_specied_industry: data.SR_specied_industry,
        industry: data.industry,
        summary: data.summary,
        location: data.address,
        is_hiring: data.is_hiring_person == 'true' ? true : false,
        companyId: data.company_id,
        sectorId: Number(data.sector_id),
        countryId: data.country_id ? Number(data.country_id) : null,
        profile_source: PersonSource.JOB_POST_SCRAPPER,
        person_type: PersonType.JOB_POST_LEAD,
        scrapper_profile_name: data.scrapper_profile_name,
      });

      if (data.phone_number) {
        await this.phoneRepository.save({
          phone_number: data.phone_number,
          phone_type:
            data.is_hiring_person == 'true'
              ? PersonPhoneType.BUSINESS
              : PersonPhoneType.PERSONAL,
          personId: newPerson.id,
        });
      }

      const savedPerson = await this.peopleRepository.save(newPerson);
      console.log('New person added by scrapper');
      return {
        message: 'Person added successfully.',
        person: savedPerson,
      };
    } catch (error) {
      console.log(
        'Error adding person: because scrapper sending country id = 0',
      );
      throw new InternalServerErrorException(
        `Failed to add person: ${error.message}`,
      );
    }
  }

  async getCompanyLinkWithRegion() {
    const getQuery = await this.companyScrapperControlRepository.find();
    const singleQuery = getQuery[0];

    let whereCondition = {} as any;

    if (singleQuery.is_default == 'false' && singleQuery.countryId) {
      whereCondition.countryId = singleQuery.countryId;
    }

    if (singleQuery.is_default == 'false' && singleQuery.sectorId) {
      whereCondition.sectorId = singleQuery.sectorId;
    }

    if (singleQuery.is_default == 'false' && singleQuery.company_source) {
      whereCondition.company_source = singleQuery.company_source;
    }

    try {
      const companyLink = await this.companyRepository.findOne({
        select: [
          'id',
          'profile_url',
          'profile_url_encoded',
          'countryId',
          'sectorId',
          'userId',
          'company_id',
          'region',
        ],
        where: {
          ...whereCondition,
          scrapper_level: 2,
          is_scrapped_fully: false,
        },
      });

      if (companyLink) {
        companyLink.is_scrapped_fully = true;
        await this.companyRepository.save(companyLink);
        console.log(
          'Company link with region found and marked as scrapped fully.',
        );
        return {
          message:
            'Company link with region found and marked as scrapped fully.',
          company: companyLink,
        };
      }

      const fallbackCompany = await this.companyRepository.findOne({
        select: [
          'id',
          'profile_url',
          'profile_url_encoded',
          'countryId',
          'sectorId',
          'userId',
          'company_id',
        ],
        where: whereCondition,
        order: {
          id: 'ASC',
        },
      });

      if (fallbackCompany) {
        fallbackCompany.is_scrapped_fully = true;
        await this.companyRepository.save(fallbackCompany);
        console.log('Company link found and marked as scrapped fully.');
        return {
          message: 'Company link found and marked as scrapped fully.',
          company: fallbackCompany,
        };
      }
      console.log('No company link with region found.');
      return {
        message: 'No company link with region found.',
      };
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to get company link with region: ${error.message}`,
      );
    }
  }
  // async getCompanyLinkWithRegion() {
  //   const queryRunner = this.dataSource.createQueryRunner();
  //   await queryRunner.connect();
  //   await queryRunner.startTransaction();

  //   try {
  //     const result = await queryRunner.query(
  //       `SELECT * FROM company
  //      WHERE scrapper_level = 2
  //        AND is_scrapped_fully = false
  //        AND region IS NOT NULL
  //      ORDER BY id ASC
  //      LIMIT 1
  //      FOR UPDATE SKIP LOCKED`,
  //     );

  //     const company = result[0];

  //     if (!company) {
  //       console.log('No company link with region found.');
  //       await queryRunner.commitTransaction();
  //       return { message: 'No company link with region found.' };
  //     }

  //     // Optionally: mark the row as "reserved" (e.g., set a "reserved_at" or "scrapping_in_progress" flag)
  //     // But DO NOT set is_scrapped_fully = true here!
  //     console.log(company.id);

  //     await queryRunner.commitTransaction();
  //     return {
  //       message: 'Company link reserved for scrapping.',
  //       company,
  //     };
  //   } catch (error) {
  //     console.log('ERROR:', error);
  //     await queryRunner.rollbackTransaction();
  //     throw new InternalServerErrorException(`Failed: ${error.message}`);
  //   } finally {
  //     await queryRunner.release();
  //   }
  // }

  async createScrapperDailyReport(data: ScrapperStatsDto) {
    if (data.date) {
      if (typeof data.date === 'string' && data.date.includes('T')) {
        data.date = data.date.split('T')[0];
      }
    }

    const reportData = {
      date: data.date,
      total_clicks: data.totalClicks,
      matching_criteria: data.matchingCriteria,
      unmatching_criteria: data.unmatchingCriteria,
      direct_companies: data.directCompanies,
      snr_companies: data.snrCompanies,
      existing_companies: data.existingCompanies,
      existing_direct_companies: data.existingDirectCompanies,
      existing_snr_companies: data.existingSnrCompanies,
      new_companies: data.newCompanies,
      new_direct_companies: data.newDirectCompanies,
      new_snr_companies: data.newSnrCompanies,
      region: data.region,
      login_errors: data.loginErrors,
      job_click_errors: data.jobClickErrors,
      no_member_error: data.NoMembersErrors,
      scrapper_profile_name: data.scrapper_profile_name,
    };

    try {
      const existingReport = await this.scrapperStatsRepository.findOne({
        where: {
          date: data.date,
          region: data.region,
        },
      });

      if (existingReport) {
        await this.scrapperStatsRepository.update(
          {
            date: data.date,
            region: data.region,
          },
          reportData,
        );
        return {
          message: `Report for date ${data.date} and region ${data.region} updated successfully`,
        };
      } else {
        await this.scrapperStatsRepository.save(reportData);
        return {
          message: `New report for date ${data.date} and region ${data.region} created successfully`,
        };
      }
    } catch (error) {
      console.error('Error creating scrapper daily report:', error);
      throw new InternalServerErrorException(error.message);
    }
  }

  async getDailyScrapperReport(queryParams: GetDailyScrapperReportDto) {
    const { date, region, page, pageSize } = queryParams;

    const limit = parseInt(pageSize);
    const skip = parseInt(page) * limit;

    if (!page) {
      throw new BadRequestException('Page number is required');
    }

    let whereCondition = {} as any;

    if (date) {
      whereCondition.date = date;
    }

    if (region) {
      whereCondition.region = ILike(`%${region}%`);
    }

    try {
      const report = await this.scrapperStatsRepository.findAndCount({
        where: whereCondition,
        order: {
          date: 'DESC',
        },
        take: limit,
        skip: skip,
      });

      const reportData = {
        data: report[0],
        pagination: {
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          total: report[1],
        },
      };

      return reportData;
    } catch (error) {
      console.error('Error creating scrapper daily report:', error);
      throw new InternalServerErrorException(error.message);
    }
  }
}
